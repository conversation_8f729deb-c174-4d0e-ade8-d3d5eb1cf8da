深入分析：
1，深入分析gateway网关apps/gateway/src/modules/character模块，了解角色管理相关api。
2，深入分析character服务apps/character/src/modules/character模块角色创建，登录，登出相关逻辑
3，深入分析auth服务下角色认证相关逻辑。

目前的问题：
1，CharacterAuthService.generateCharacterToken,auth服务无法保证角色信息的正确性。
2，网关层无法在全局服务gateway/auth层管理角色id的创建，认证，以及获取账号下是否存在角色。而需要通过业务服务character。

优化方案：
1，一个账号在某个区服下只能创建唯一的角色id。
2，auth服务新增character模块，负责账号所有区服的角色id维护（创建区服角色，查询区服角色信息，查询账号下所有区服的角色信息）
3，强化auth服务角色认证功能，确保角色信息的正确性。
4，auth服务只维护基本的角色信息（角色id,角色所在区服id，创建时间，最后登录时间）
5，character不再负责characterId的生成，而是角色登陆时获取注入的角色id，如果角色未初始化则初始化相关信息。
