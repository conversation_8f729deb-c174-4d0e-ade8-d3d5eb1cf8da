/**
 * Auth服务角色管理和认证完整流程测试脚本
 * 
 * 测试流程：
 * 1. 账号注册
 * 2. 账号登录
 * 3. 创建角色
 * 4. 角色登录获取角色Token
 * 5. 验证角色Token
 * 6. 刷新角色Token
 * 7. 角色登出
 * 
 * 使用方法：
 * node apps/auth/scripts/test-auth-character-flow.js
 */

const axios = require('axios');
const chalk = require('chalk');

class AuthCharacterFlowTest {
  constructor() {
    this.config = {
      GATEWAY_URL: 'http://127.0.0.1:3000',
      AUTH_URL: 'http://127.0.0.1:3100',
      TIMEOUT: 30000
    };

    this.serverId = 'server001';
    this.testUserId = null;
    this.accessToken = null;
    this.characterId = null;
    this.characterToken = null;
    
    // 生成唯一的测试用户信息
    const timestamp = Date.now();
    this.testUser = {
      username: `authtest_${timestamp}`,
      email: `authtest_${timestamp}@example.com`,
      password: 'SecureP@ssw0rd!',
      confirmPassword: 'SecureP@ssw0rd!',
      acceptTerms: true,
      profile: {
        firstName: 'Auth',
        lastName: 'Test'
      }
    };
  }

  /**
   * 日志输出
   */
  log(message, color = 'white') {
    console.log(chalk[color](message));
  }

  /**
   * 错误处理
   */
  handleError(error, context) {
    this.log(`❌ ${context}失败: ${error.message}`, 'red');
    if (error.response) {
      this.log(`HTTP状态: ${error.response.status}`, 'yellow');
      this.log(`响应数据: ${JSON.stringify(error.response.data, null, 2)}`, 'yellow');
    }
    throw error;
  }

  /**
   * HTTP健康检查
   */
  async checkHealth() {
    this.log('\n=== 第1步：服务健康检查 ===', 'blue');
    
    const services = [
      { name: '网关服务', url: `${this.config.GATEWAY_URL}/health` },
      { name: 'Auth服务', url: `${this.config.AUTH_URL}/health` }
    ];

    for (const service of services) {
      try {
        const response = await axios.get(service.url, { timeout: 5000 });
        if (response.status === 200) {
          this.log(`✅ ${service.name}健康检查通过`, 'green');
        } else {
          throw new Error(`健康检查返回状态: ${response.status}`);
        }
      } catch (error) {
        this.handleError(error, `${service.name}健康检查`);
      }
    }
  }

  /**
   * 账号注册
   */
  async registerAccount() {
    this.log('\n=== 第2步：账号注册 ===', 'blue');
    this.log(`注册用户: ${this.testUser.username}`, 'gray');
    
    try {
      const response = await axios.post(`${this.config.GATEWAY_URL}/api/auth/auth/register`, this.testUser, { timeout: 10000 });

      if (response.data.success) {
        this.log('✅ 账号注册成功', 'green');
        return true;
      } else {
        throw new Error(`注册失败: ${response.data.message}`);
      }
    } catch (error) {
      this.handleError(error, '账号注册');
    }
  }

  /**
   * 账号登录
   */
  async loginAccount() {
    this.log('\n=== 第3步：账号登录 ===', 'blue');
    
    try {
      const response = await axios.post(
        `${this.config.GATEWAY_URL}/api/auth/auth/login`,
        {
          identifier: this.testUser.username,
          password: this.testUser.password
        },
        {
          timeout: this.config.TIMEOUT,
          headers: { 'Content-Type': 'application/json' }
        }
      );

      if (response.data.success && response.data.data.tokens.accessToken) {
        this.accessToken = response.data.data.tokens.accessToken;
        this.testUserId = response.data.data.user.id;
        this.log('✅ 账号登录成功', 'green');
        this.log(`用户ID: ${this.testUserId}`, 'gray');
        this.log(`Token: ${this.accessToken}`, 'gray');
        this.log(`Token长度: ${this.accessToken.length}`, 'gray');
        return true;
      } else {
        throw new Error(`登录失败: ${response.data.message}`);
      }
    } catch (error) {
      this.handleError(error, '账号登录');
    }
  }

  /**
   * 验证账号Token
   */
  async verifyAccountToken() {
    this.log('\n=== 第4步：验证账号Token ===', 'blue');

    try {
      const response = await axios.post(
        `${this.config.GATEWAY_URL}/api/auth/auth/verify-token`,
        {
          token: this.accessToken
        },
        {
          timeout: this.config.TIMEOUT,
          headers: { 'Content-Type': 'application/json' }
        }
      );

      if (response.data.success && response.data.data.valid) {
        this.log('✅ 账号Token验证成功', 'green');
        this.log(`Token有效性: ${response.data.data.valid}`, 'gray');
        return true;
      } else {
        throw new Error(`Token验证失败: ${response.data.message}`);
      }
    } catch (error) {
      this.handleError(error, '账号Token验证');
    }
  }

  /**
   * 创建角色
   */
  async createCharacter() {
    this.log('\n=== 第5步：创建角色 ===', 'blue');
    
    const characterName = `Char_${Date.now().toString().slice(-8)}`;
    this.log(`角色名称: ${characterName}`, 'gray');
    this.log(`区服ID: ${this.serverId}`, 'gray');
    
    try {
      const response = await axios.post(
        `${this.config.GATEWAY_URL}/api/auth/characters`,
        {
          userId: this.testUserId, // 测试用，生产环境从认证用户获取
          serverId: this.serverId,
          characterName: characterName,
          displayName: `显示_${characterName}`,
          faceIcon: 15
        },
        {
          timeout: this.config.TIMEOUT,
          headers: {
            'Authorization': `Bearer ${this.accessToken}`,
            'Content-Type': 'application/json'
          }
        }
      );

      if (response.data.success && response.data.data) {
        this.characterId = response.data.data.characterId;
        this.log('✅ 角色创建成功', 'green');
        this.log(`角色ID: ${this.characterId}`, 'gray');
        return true;
      } else {
        throw new Error(`角色创建失败: ${response.data.message}`);
      }
    } catch (error) {
      this.handleError(error, '角色创建');
    }
  }

  /**
   * 角色登录获取角色Token
   */
  async loginCharacter() {
    this.log('\n=== 第6步：角色登录获取角色Token ===', 'blue');
    this.log(`角色ID: ${this.characterId}`, 'gray');
    
    try {
      const response = await axios.post(
        `${this.config.GATEWAY_URL}/api/auth/character-auth/login`,
        {
          characterId: this.characterId,
          serverId: this.serverId
        },
        {
          timeout: this.config.TIMEOUT,
          headers: {
            'Authorization': `Bearer ${this.accessToken}`,
            'Content-Type': 'application/json'
          }
        }
      );

      if (response.data.success && response.data.data.characterToken) {
        this.characterToken = response.data.data.characterToken;
        this.log('✅ 角色登录成功', 'green');
        this.log(`角色Token: ${this.characterToken}`, 'gray');
        this.log(`角色Token长度: ${this.characterToken.length}`, 'gray');
        this.log(`Token过期时间: ${response.data.data.expiresAt}`, 'gray');
        return true;
      } else {
        throw new Error(`角色登录失败: ${response.data.message}`);
      }
    } catch (error) {
      this.handleError(error, '角色登录');
    }
  }

  /**
   * 验证角色Token
   */
  async verifyCharacterToken() {
    this.log('\n=== 第7步：验证角色Token ===', 'blue');
    
    try {
      const response = await axios.post(
        `${this.config.GATEWAY_URL}/api/auth/character-auth/verify`,
        {
          characterToken: this.characterToken
        },
        {
          timeout: this.config.TIMEOUT,
          headers: {
            'Authorization': `Bearer ${this.accessToken}`,
            'Content-Type': 'application/json'
          }
        }
      );

      if (response.data.success && response.data.data.valid) {
        this.log('✅ 角色Token验证成功', 'green');
        this.log(`Token有效性: ${response.data.data.valid}`, 'gray');
        return true;
      } else {
        throw new Error(`Token验证失败: ${response.data.message}`);
      }
    } catch (error) {
      this.handleError(error, '角色Token验证');
    }
  }

  /**
   * 刷新角色Token
   */
  async refreshCharacterToken() {
    this.log('\n=== 第8步：刷新角色Token ===', 'blue');
    
    const oldToken = this.characterToken;
    
    try {
      const response = await axios.post(
        `${this.config.GATEWAY_URL}/api/auth/auth/character/refresh`,
        {
          characterToken: this.characterToken
        },
        {
          timeout: this.config.TIMEOUT,
          headers: {
            'Authorization': `Bearer ${this.accessToken}`,
            'Content-Type': 'application/json'
          }
        }
      );

      if (response.data.success && response.data.data.characterToken) {
        this.characterToken = response.data.data.characterToken;
        this.log('✅ 角色Token刷新成功', 'green');
        this.log(`新Token长度: ${this.characterToken.length}`, 'gray');
        this.log(`Token过期时间: ${response.data.data.expiresAt}`, 'gray');
        this.log(`Token已更新: ${oldToken !== this.characterToken}`, 'gray');
        return true;
      } else {
        throw new Error(`Token刷新失败: ${response.data.message}`);
      }
    } catch (error) {
      this.handleError(error, '角色Token刷新');
    }
  }

  /**
   * 角色登出
   */
  async logoutCharacter() {
    this.log('\n=== 第9步：角色登出 ===', 'blue');
    
    try {
      const response = await axios.post(
        `${this.config.GATEWAY_URL}/api/auth/auth/character/logout`,
        {
          characterToken: this.characterToken
        },
        {
          timeout: this.config.TIMEOUT,
          headers: {
            'Authorization': `Bearer ${this.accessToken}`,
            'Content-Type': 'application/json'
          }
        }
      );

      if (response.data.success) {
        this.log('✅ 角色登出成功', 'green');
        return true;
      } else {
        throw new Error(`角色登出失败: ${response.data.message}`);
      }
    } catch (error) {
      this.handleError(error, '角色登出');
    }
  }

  /**
   * 运行完整测试流程
   */
  async runFullTest() {
    this.log('🚀 开始Auth服务角色管理和认证完整流程测试', 'cyan');
    this.log(`测试时间: ${new Date().toLocaleString()}`, 'gray');
    
    try {
      await this.checkHealth();
      await this.registerAccount();
      await this.loginAccount();
      await this.verifyAccountToken();
      await this.createCharacter();
      await this.loginCharacter();
      await this.verifyCharacterToken();
      await this.refreshCharacterToken();
      await this.logoutCharacter();
      
      this.log('\n🎉 所有测试步骤完成！', 'green');
      this.log('\n📋 测试结果总结:', 'cyan');
      this.log(`✅ 账号注册: 成功`, 'green');
      this.log(`✅ 账号登录: 成功`, 'green');
      this.log(`✅ 账号Token验证: 成功`, 'green');
      this.log(`✅ 角色创建: 成功 (${this.characterId})`, 'green');
      this.log(`✅ 角色登录: 成功`, 'green');
      this.log(`✅ Token验证: 成功`, 'green');
      this.log(`✅ Token刷新: 成功`, 'green');
      this.log(`✅ 角色登出: 成功`, 'green');
      
    } catch (error) {
      this.log('\n❌ 测试流程失败', 'red');
      this.log(`错误信息: ${error.message}`, 'red');
      process.exit(1);
    }
  }
}

// 主执行函数
async function main() {
  const tester = new AuthCharacterFlowTest();
  await tester.runFullTest();
}

// 运行测试
if (require.main === module) {
  main().catch(error => {
    console.error('测试执行失败:', error);
    process.exit(1);
  });
}

module.exports = AuthCharacterFlowTest;
