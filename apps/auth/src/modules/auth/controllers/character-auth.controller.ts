import {
  Controller,
  Get,
  Post,
  Body,
  Headers,
  HttpCode,
  HttpStatus,
  UseGuards,
  Req,
  Logger,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiHeader } from '@nestjs/swagger';
import { MessagePattern, Payload } from '@nestjs/microservices';

// 守卫和装饰器
import { JwtAuthGuard } from '../../security/guards/jwt-auth.guard';
import { CurrentUser } from '@auth/common/decorators/current-user.decorator';

// 服务
import { CharacterAuthService } from '../services/character-auth.service';

// DTO
import { CharacterLoginDto } from '../dto/character-login.dto';
import { CharacterLoginResponseDto } from '../dto/character-login-response.dto';
import { SessionInfoResponseDto } from '../dto/session-info-response.dto';
import { RefreshTokenResponseDto } from '../dto/refresh-token-response.dto';

// 类型
import { User } from '@auth/modules/user/entities/user.entity';

/**
 * 角色认证控制器
 * 提供角色登录、角色登出、会话管理等HTTP接口
 */
@ApiTags('角色认证')
@Controller('character-auth')
export class CharacterAuthController {
  private readonly logger = new Logger(CharacterAuthController.name);

  constructor(private readonly characterAuthService: CharacterAuthService) {}

  /**
   * 生成角色Token（纯认证接口）
   * POST /api/auth/character-auth/generate-token
   *
   * 此接口专为网关层调用设计，不包含业务逻辑验证
   */
  @Post('generate-token')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: '生成角色Token',
    description: '为指定角色生成认证Token，由网关层调用，不验证角色存在性'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '角色Token生成成功',
    type: CharacterLoginResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: '请求参数错误',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: '用户不存在',
  })
  async generateCharacterToken(
    @Body() tokenRequest: {
      userId: string;
      characterId: string;
      serverId: string;
      characterName?: string; // 可选，由Character模块提供
      sessionData?: any;
    }
  ): Promise<CharacterLoginResponseDto> {
    this.logger.log(`🔑 [API] 生成角色Token: userId=${tokenRequest.userId}, characterId=${tokenRequest.characterId}`);

    // 使用新的增强版方法
    const result = await this.characterAuthService.generateCharacterToken({
      userId: tokenRequest.userId,
      characterId: tokenRequest.characterId,
      serverId: tokenRequest.serverId,
    });

    return {
      success: true,
      message: '角色Token生成成功',
      data: result,
    };
  }



  /**
   * 角色登出
   * POST /api/auth/character-auth/logout
   */
  @Post('logout')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: '角色登出',
    description: '使用角色Token退出当前角色会话'
  })
  @ApiHeader({
    name: 'Authorization',
    description: '角色级Bearer Token',
    required: true,
  })
  @ApiResponse({
    status: 200,
    description: '登出成功',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: { type: 'string', example: '角色登出成功' },
      },
    }
  })
  @ApiResponse({ status: 401, description: '未授权或Token无效' })
  @ApiResponse({ status: 500, description: '服务器错误' })
  async characterLogout(@Headers('authorization') authorization: string): Promise<any> {
    this.logger.log('角色登出请求');

    try {
      // 提取角色Token
      const characterToken = authorization?.replace('Bearer ', '');
      if (!characterToken) {
        throw new Error('缺少角色认证Token');
      }

      await this.characterAuthService.characterLogout(characterToken);

      return {
        success: true,
        message: '角色登出成功',
      };

    } catch (error) {
      this.logger.error('角色登出失败', error);
      throw error;
    }
  }

  /**
   * 刷新角色Token
   * POST /api/auth/character-auth/refresh
   */
  @Post('refresh')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: '刷新角色Token',
    description: '使用当前角色Token获取新的角色Token'
  })
  @ApiHeader({
    name: 'Authorization',
    description: '角色级Bearer Token',
    required: true,
  })
  @ApiResponse({
    status: 200,
    description: '刷新成功',
    type: RefreshTokenResponseDto
  })
  @ApiResponse({ status: 401, description: '未授权或Token无效' })
  @ApiResponse({ status: 500, description: '服务器错误' })
  async refreshCharacterToken(@Headers('authorization') authorization: string): Promise<RefreshTokenResponseDto> {
    this.logger.log('刷新角色Token请求');

    try {
      // 提取角色Token
      const characterToken = authorization?.replace('Bearer ', '');
      if (!characterToken) {
        throw new Error('缺少角色认证Token');
      }

      const newToken = await this.characterAuthService.refreshCharacterToken(characterToken);

      return {
        success: true,
        message: '角色Token刷新成功',
        data: {
          characterToken: newToken,
          tokenType: 'Bearer',
          expiresIn: 4 * 3600, // 4小时
        },
      };

    } catch (error) {
      this.logger.error('刷新角色Token失败', error);
      throw error;
    }
  }

  /**
   * 获取当前会话信息
   * GET /api/auth/character-auth/session
   */
  @Get('session')
  @ApiOperation({
    summary: '获取当前会话信息',
    description: '使用角色Token获取当前会话的详细信息'
  })
  @ApiHeader({
    name: 'Authorization',
    description: '角色级Bearer Token',
    required: true,
  })
  @ApiResponse({
    status: 200,
    description: '获取成功',
    type: SessionInfoResponseDto
  })
  @ApiResponse({ status: 401, description: '未授权或Token无效' })
  @ApiResponse({ status: 404, description: '会话不存在' })
  @ApiResponse({ status: 500, description: '服务器错误' })
  async getCurrentSession(@Headers('authorization') authorization: string): Promise<SessionInfoResponseDto> {
    this.logger.log('获取会话信息请求');

    try {
      // 提取角色Token
      const characterToken = authorization?.replace('Bearer ', '');
      if (!characterToken) {
        throw new Error('缺少角色认证Token');
      }

      const result = await this.characterAuthService.getCurrentSession(characterToken);

      return {
        success: true,
        message: '获取会话信息成功',
        data: result,
      };

    } catch (error) {
      this.logger.error('获取会话信息失败', error);
      throw error;
    }
  }

  /**
   * 健康检查
   * GET /api/auth/character-auth/health
   */
  @Get('health')
  @ApiOperation({ 
    summary: '健康检查',
    description: '检查角色认证服务的健康状态'
  })
  @ApiResponse({ 
    status: 200, 
    description: '服务正常',
    schema: {
      type: 'object',
      properties: {
        status: { type: 'string', example: 'ok' },
        timestamp: { type: 'string', format: 'date-time' },
        service: { type: 'string', example: 'character-auth' },
      },
    }
  })
  async healthCheck(): Promise<any> {
    return {
      status: 'ok',
      timestamp: new Date().toISOString(),
      service: 'character-auth',
    };
  }

  // ==================== 微服务接口 - 服务间通信 ====================

  /**
   * 生成角色Token - 微服务调用
   */
  @MessagePattern('character-auth.generateCharacterToken')
  async generateCharacterTokenMicroservice(@Payload() data: {
    userId: string;
    characterId: string;
    serverId: string;
    characterName?: string; // 可选，由Character模块提供
    sessionData?: any;
  }): Promise<CharacterLoginResponseDto> {
    console.log(`📨 收到微服务调用: generateCharacterToken`);
    console.log(`📨 请求数据: ${JSON.stringify(data)}`);

    try {
      // 使用新的增强版方法
      const result = await this.characterAuthService.generateCharacterToken({
        userId: data.userId,
        characterId: data.characterId,
        serverId: data.serverId,
      });
      console.log(`✅ 微服务调用成功: ${JSON.stringify(result)}`);
      return {
        success: true,
        message: '角色Token生成成功',
        data: result,
      };
    } catch (error) {
      console.error(`❌ 微服务调用失败: ${error.message}`);
      throw error; // 微服务调用应该抛出异常，而不是返回错误对象
    }
  }

  /**
   * 角色登出 - 微服务调用
   */
  @MessagePattern('character-auth.characterLogout')
  async characterLogoutMicroservice(@Payload() data: { characterToken: string }) {
    console.log(`📨 收到微服务调用: characterLogout`);
    console.log(`📨 请求数据: ${JSON.stringify(data)}`);

    try {
      await this.characterAuthService.characterLogout(data.characterToken);
      console.log(`✅ 微服务调用成功: 角色登出完成`);
      return {
        success: true,
        message: '角色登出成功'
      };
    } catch (error) {
      console.error(`❌ 微服务调用失败: ${error.message}`);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 刷新角色Token - 微服务调用
   */
  @MessagePattern('character-auth.refreshCharacterToken')
  async refreshCharacterTokenMicroservice(@Payload() data: { characterToken: string }): Promise<RefreshTokenResponseDto> {
    console.log(`📨 收到微服务调用: refreshCharacterToken`);
    console.log(`📨 请求数据: ${JSON.stringify(data)}`);

    try {
      const newToken = await this.characterAuthService.refreshCharacterToken(data.characterToken);
      console.log(`✅ 微服务调用成功: Token刷新完成`);
      return {
        success: true,
        message: '角色Token刷新成功',
        data: {
          characterToken: newToken,
          tokenType: 'Bearer',
          expiresIn: 4 * 3600, // 4小时
        },
      };
    } catch (error) {
      console.error(`❌ 微服务调用失败: ${error.message}`);
      throw error; // 微服务调用应该抛出异常，而不是返回错误对象
    }
  }

  /**
   * 获取当前会话信息 - 微服务调用
   */
  @MessagePattern('character-auth.getCurrentSession')
  async getCurrentSessionMicroservice(@Payload() data: { characterToken: string }): Promise<SessionInfoResponseDto> {
    console.log(`📨 收到微服务调用: getCurrentSession`);
    console.log(`📨 请求数据: ${JSON.stringify(data)}`);

    try {
      const result = await this.characterAuthService.getCurrentSession(data.characterToken);
      console.log(`✅ 微服务调用成功: ${JSON.stringify(result)}`);
      return {
        success: true,
        message: '获取会话信息成功',
        data: result,
      };
    } catch (error) {
      console.error(`❌ 微服务调用失败: ${error.message}`);
      throw error; // 微服务调用应该抛出异常，而不是返回错误对象
    }
  }

  /**
   * 验证角色Token - 微服务调用
   */
  @MessagePattern('character-auth.verifyCharacterToken')
  async verifyCharacterTokenMicroservice(@Payload() data: { characterToken: string }): Promise<{ valid: boolean; data?: any; error?: string }> {
    console.log(`📨 收到微服务调用: verifyCharacterToken`);
    console.log(`📨 请求数据: ${JSON.stringify(data)}`);

    try {
      // 通过获取会话信息来验证Token
      const sessionInfo = await this.characterAuthService.getCurrentSession(data.characterToken);
      console.log(`✅ 微服务调用成功: Token验证通过`);
      return {
        valid: true,
        data: {
          userId: sessionInfo.userId,
          characterId: sessionInfo.characterId,
          serverId: sessionInfo.serverId,
          characterName: sessionInfo.characterName,
          sessionId: sessionInfo.sessionId
        }
      };
    } catch (error) {
      console.error(`❌ 微服务调用失败: ${error.message}`);
      return {
        valid: false,
        error: error.message
      };
    }
  }

  /**
   * 批量终止用户角色会话 - 微服务调用
   */
  @MessagePattern('character-auth.terminateUserCharacterSessions')
  async terminateUserCharacterSessionsMicroservice(@Payload() data: { userId: string }): Promise<{ success: boolean; terminatedCount?: number; message?: string; error?: string }> {
    console.log(`📨 收到微服务调用: terminateUserCharacterSessions`);
    console.log(`📨 请求数据: ${JSON.stringify(data)}`);

    try {
      // 调用CharacterSessionService的方法来终止用户所有角色会话
      const terminatedCount = await this.characterAuthService.terminateAllUserSessions(data.userId);
      console.log(`✅ 微服务调用成功: 终止了${terminatedCount}个会话`);
      return {
        success: true,
        terminatedCount,
        message: `成功终止${terminatedCount}个角色会话`
      };
    } catch (error) {
      console.error(`❌ 微服务调用失败: ${error.message}`);
      return {
        success: false,
        error: error.message
      };
    }
  }
}
