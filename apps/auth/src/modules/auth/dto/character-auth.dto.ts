import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsOptional } from 'class-validator';

/**
 * 角色登录请求DTO
 */
export class CharacterLoginDto {
  @ApiProperty({
    description: '角色ID',
    example: 'char_server001_abc123_def456',
  })
  @IsString()
  @IsNotEmpty()
  characterId: string;

  @ApiProperty({
    description: '区服ID',
    example: 'server001',
  })
  @IsString()
  @IsNotEmpty()
  serverId: string;
}

/**
 * 角色登出请求DTO
 */
export class CharacterLogoutDto {
  @ApiProperty({
    description: '角色Token',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
  })
  @IsString()
  @IsNotEmpty()
  characterToken: string;
}

/**
 * 刷新角色Token请求DTO
 */
export class RefreshCharacterTokenDto {
  @ApiProperty({
    description: '当前角色Token',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
  })
  @IsString()
  @IsNotEmpty()
  characterToken: string;
}

/**
 * 验证角色Token请求DTO
 */
export class VerifyCharacterTokenDto {
  @ApiProperty({
    description: '角色Token',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
  })
  @IsString()
  @IsNotEmpty()
  characterToken: string;
}

/**
 * 角色Token响应DTO
 */
export class CharacterTokenResponseDto {
  @ApiProperty({
    description: '是否成功',
    example: true,
  })
  success: boolean;

  @ApiProperty({
    description: '响应消息',
    example: 'Token刷新成功',
  })
  message: string;

  @ApiProperty({
    description: '响应数据',
  })
  data: {
    characterToken: string;
    expiresIn: number;
    expiresAt: Date;
  };
}

/**
 * 角色登录响应DTO
 */
export class CharacterLoginResponseDto {
  @ApiProperty({
    description: '是否成功',
    example: true,
  })
  success: boolean;

  @ApiProperty({
    description: '响应消息',
    example: '角色登录成功',
  })
  message: string;

  @ApiProperty({
    description: '响应数据',
  })
  data: {
    characterToken: string;
    expiresIn: number;
    expiresAt: Date;
    character: {
      characterId: string;
      name: string;
      displayName?: string;
      level: number;
      serverId: string;
      userId: string;
      avatar?: string;
    };
    server: {
      id: string;
      name: string;
      status: string;
    };
    session: {
      id: string;
      expiresAt: Date;
    };
  };
}

/**
 * 角色Token验证响应DTO
 */
export class CharacterTokenValidationResponseDto {
  @ApiProperty({
    description: '是否成功',
    example: true,
  })
  success: boolean;

  @ApiProperty({
    description: '响应消息',
    example: 'Token验证成功',
  })
  message: string;

  @ApiProperty({
    description: '验证结果',
  })
  data: {
    valid: boolean;
    payload?: any;
    error?: string;
  };
}
