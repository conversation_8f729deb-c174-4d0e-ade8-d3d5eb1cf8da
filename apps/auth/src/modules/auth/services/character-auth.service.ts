import { Injectable, Logger, NotFoundException, BadRequestException, UnauthorizedException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

// 核心服务
import { RedisService } from '@common/redis';
import { UsersService } from '@auth/modules/user/services/users.service';

// 角色会话服务
import { CharacterSessionService } from './character-session.service';

// 角色管理服务
import { CharacterService } from '../../character/services/character.service';

// 类型定义
import { JwtPayload, CharacterTokenPair,JwtService } from './jwt.service';



/**
 * 角色认证服务
 * 负责角色登录、角色登出、角色会话管理等核心功能
 */
@Injectable()
export class CharacterAuthService {
  private readonly logger = new Logger(CharacterAuthService.name);

  constructor(
    private readonly configService: ConfigService,
    private readonly redisService: RedisService,
    private readonly usersService: UsersService,
    private readonly jwtService: JwtService,
    private readonly characterSessionService: CharacterSessionService,
    private readonly characterService: CharacterService,
  ) {}

  /**
   * 生成角色Token
   *
   */
  async generateCharacterToken(request: {
    userId: string;
    characterId: string;
    serverId: string;
  }): Promise<CharacterLoginResponse> {
    this.logger.log(`🔑 生成角色Token: userId=${request.userId}, characterId=${request.characterId}`);

    try {
      // 1. 验证用户存在（基础验证）
      const user = await this.usersService.findById(request.userId);
      if (!user) {
        throw new NotFoundException('用户不存在');
      }

      // 2. 通过Character模块验证角色归属（权威验证）
      const character = await this.validateCharacterOwnership(
        request.userId,
        request.characterId,
        request.serverId
      );

      // 3. 更新角色登录信息
      await this.updateCharacterLoginInfo(request.characterId);

      // 4. 检查并终止现有会话（单设备登录）
      await this.terminateExistingCharacterSessions(request.userId);

      // 5. 创建新会话
      const session = await this.characterSessionService.createSession({
        userId: request.userId,
        characterId: character.characterId,
        serverId: character.serverId,
        serverName: `区服${character.serverId}`,
        expiresAt: new Date(Date.now() + 4 * 3600 * 1000), // 4小时
      });

      // 6. 生成Token（包含完整验证信息）
      const tokenPayload = {
        sub: request.userId,
        username: user.username,
        email: user.email,
        roles: user.roles || [],
        permissions: user.permissions || [],
        sessionId: session.id,
        characterId: character.characterId,
        serverId: character.serverId,
        characterName: character.characterName,
        scope: 'character' as const,
      };

      const characterToken = this.jwtService.generateCharacterToken(tokenPayload);

      // 7. 记录登录历史
      await this.recordCharacterLogin(
        request.userId,
        request.serverId,
        request.characterId
      );

      this.logger.log(`✅ 角色Token生成成功: ${character.characterId}`);

      return {
        characterToken,
        expiresIn: 4 * 3600,
        expiresAt: new Date(Date.now() + 4 * 3600 * 1000),
        character: {
          characterId: character.characterId,
          name: character.characterName,
          level: character.metadata?.level || 1,
          serverId: character.serverId,
          userId: character.userId,
        },
        server: {
          id: character.serverId,
          name: `区服${character.serverId}`,
          status: 'active',
          openTime: new Date('2024-01-01'),
          maxPlayers: 10000,
        },
        session: {
          id: session.id,
          expiresAt: session.expiresAt,
        },
      };

    } catch (error) {
      this.logger.error(`❌ 角色Token生成失败: ${request.characterId}`, error);
      throw error;
    }
  }

  /**
   * 验证角色Token
   */
  async verifyCharacterToken(token: string): Promise<any> {
    try {
      // 1. 验证Token并获取载荷
      const payload = await this.jwtService.verifyCharacterToken(token);

      // 2. 验证会话状态
      const session = await this.characterSessionService.getSession(payload.sessionId);
      if (!session || session.expiresAt < new Date()) {
        throw new UnauthorizedException('会话已过期');
      }

      return payload;
    } catch (error) {
      this.logger.error(`❌ 角色Token验证失败: ${error.message}`);
      throw error;
    }
  }


  /**
   * 私有方法：终止现有角色会话
   */
  private async terminateExistingCharacterSessions(userId: string): Promise<void> {
    try {
      await this.characterSessionService.terminateUserSessions(userId);
    } catch (error) {
      this.logger.warn(`终止现有会话失败: ${userId}`, error);
      // 不抛出错误，允许继续创建新会话
    }
  }

  /**
   * 私有方法：记录角色登录历史
   */
  private async recordCharacterLogin(userId: string, serverId: string, characterId: string): Promise<void> {
    try {
      const loginRecord = {
        userId,
        serverId,
        characterId,
        loginTime: new Date(),
        action: 'character_login',
      };

      // 记录到Redis（用于快速查询）
      const loginKey = `character:login:${userId}:${serverId}:${characterId}`;
      await this.redisService.set(loginKey, JSON.stringify(loginRecord), 24 * 3600, 'global'); // 24小时

      // 更新用户最后登录信息
      const lastLoginKey = `user:${userId}:last_character_login`;
      await this.redisService.set(lastLoginKey, JSON.stringify({
        serverId,
        characterId,
        loginTime: new Date(),
      }), 7 * 24 * 3600, 'global'); // 7天

      this.logger.debug(`角色登录历史记录完成: ${userId}@${serverId}:${characterId}`);
    } catch (error) {
      this.logger.warn(`记录角色登录历史失败: ${userId}`, error);
      // 不抛出错误，不影响主要登录流程
    }
  }

  /**
   * 私有方法：记录角色登出历史
   */
  private async recordCharacterLogout(userId: string, serverId: string, characterId: string): Promise<void> {
    try {
      const logoutRecord = {
        userId,
        serverId,
        characterId,
        logoutTime: new Date(),
        action: 'character_logout',
      };

      // 记录到Redis
      const logoutKey = `character:logout:${userId}:${serverId}:${characterId}`;
      await this.redisService.set(logoutKey, JSON.stringify(logoutRecord), 24 * 3600, 'global'); // 24小时

      // 清除登录记录
      const loginKey = `character:login:${userId}:${serverId}:${characterId}`;
      await this.redisService.del(loginKey, 'global');

      this.logger.debug(`角色登出历史记录完成: ${userId}@${serverId}:${characterId}`);
    } catch (error) {
      this.logger.warn(`记录角色登出历史失败: ${userId}`, error);
      // 不抛出错误，不影响主要登出流程
    }
  }

  /**
   * 私有方法：清理角色缓存
   */
  private async clearCharacterCache(characterId: string): Promise<void> {
    try {
      // 清理角色相关的缓存数据
      const cacheKeys = [
        `character:info:${characterId}`,
        `character:status:${characterId}`,
        `character:permissions:${characterId}`,
      ];

      const deletePromises = cacheKeys.map(key =>
        this.redisService.del(key, 'global')
      );

      await Promise.allSettled(deletePromises);
      this.logger.debug(`角色缓存清理完成: ${characterId}`);
    } catch (error) {
      this.logger.warn(`清理角色缓存失败: ${characterId}`, error);
      // 不抛出错误，不影响主要业务流程
    }
  }

  /**
   * 终止用户所有角色会话
   * 用于微服务调用，当用户登出或被封禁时调用
   */
  async terminateAllUserSessions(userId: string): Promise<number> {
    try {
      this.logger.log(`🔒 开始终止用户所有角色会话: ${userId}`);

      // 获取用户所有活跃会话
      const activeSessions = await this.characterSessionService.findActiveByUserId(userId);
      const sessionCount = activeSessions.length;

      if (sessionCount === 0) {
        this.logger.log(`📝 用户无活跃角色会话: ${userId}`);
        return 0;
      }

      // 终止所有会话
      await this.characterSessionService.terminateUserSessions(userId);

      this.logger.log(`✅ 成功终止用户所有角色会话: ${userId}, 共${sessionCount}个会话`);
      return sessionCount;

    } catch (error) {
      this.logger.error(`❌ 终止用户角色会话失败: ${userId}`, error);
      throw new BadRequestException(`终止用户会话失败: ${error.message}`);
    }
  }

  /**
   * 通过Character模块验证角色归属（权威验证）
   */
  private async validateCharacterOwnership(
    userId: string,
    characterId: string,
    serverId: string
  ): Promise<any> {
    try {
      // 1. 验证角色归属
      await this.characterService.validateCharacterOwnership(userId, characterId, serverId);

      // 2. 获取角色详细信息
      const character = await this.characterService.getCharacterDetail(characterId);

      this.logger.log(`✅ 角色归属验证成功: userId=${userId}, characterId=${characterId}`);

      return character;

    } catch (error) {
      this.logger.error(`❌ 角色归属验证失败: ${characterId}`, error);
      throw new UnauthorizedException('角色不存在或不属于当前用户');
    }
  }

  /**
   * 更新角色登录信息
   */
  private async updateCharacterLoginInfo(characterId: string): Promise<void> {
    try {
      await this.characterService.updateLoginInfo(characterId);
      this.logger.log(`✅ 更新角色登录信息成功: ${characterId}`);
    } catch (error) {
      this.logger.error(`❌ 更新角色登录信息失败: ${characterId}`, error);
      // 不抛出错误，允许继续登录流程
    }
  }

  /**
   * 角色登出
   */
  async characterLogout(characterToken: string): Promise<void> {
    try {
      // 1. 验证Token并获取会话信息
      const payload = await this.jwtService.verifyCharacterToken(characterToken);

      // 2. 终止会话
      await this.characterSessionService.terminateSession(payload.sessionId);

      // 3. 将Token加入黑名单
      await this.jwtService.revokeToken(characterToken);

      this.logger.log(`✅ 角色登出成功: ${payload.characterId}`);

    } catch (error) {
      this.logger.error(`❌ 角色登出失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 刷新角色Token
   */
  async refreshCharacterToken(oldToken: string): Promise<any> {
    try {
      // 1. 验证旧Token
      const payload = await this.jwtService.verifyCharacterToken(oldToken);

      // 2. 检查会话是否仍然有效
      const session = await this.characterSessionService.getSession(payload.sessionId);
      if (!session || session.expiresAt < new Date()) {
        throw new UnauthorizedException('会话已过期');
      }

      // 3. 验证角色状态
      await this.validateCharacterOwnership(
        payload.sub,
        payload.characterId,
        payload.serverId
      );

      // 4. 生成新Token
      const newTokenPayload = {
        ...payload,
        iat: Math.floor(Date.now() / 1000),
        exp: Math.floor((Date.now() + 4 * 3600 * 1000) / 1000),
      };

      const newToken = this.jwtService.generateCharacterToken(newTokenPayload);

      // 5. 将旧Token加入黑名单
      await this.jwtService.revokeToken(oldToken);

      // 6. 更新会话过期时间
      await this.characterSessionService.extendSession(
        payload.sessionId,
        4 * 3600 // 4小时，以秒为单位
      );

      this.logger.log(`✅ 角色Token刷新成功: ${payload.characterId}`);

      return {
        characterToken: newToken,
        expiresIn: 4 * 3600,
        expiresAt: new Date(Date.now() + 4 * 3600 * 1000),
      };

    } catch (error) {
      this.logger.error(`❌ 角色Token刷新失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 获取当前会话信息
   */
  async getCurrentSession(characterToken: string): Promise<any> {
    this.logger.log('获取当前会话信息');

    try {
      const payload = await this.jwtService.verifyCharacterToken(characterToken);
      const session = await this.characterSessionService.getSession(payload.sessionId);

      if (!session || session.expiresAt < new Date()) {
        throw new UnauthorizedException('会话已过期');
      }

      return {
        sessionId: session.id,
        userId: session.userId,
        characterId: session.characterId,
        characterName: `角色${session.characterId}`, // CharacterJwtPayload没有characterName字段
        serverId: session.serverId,
        serverName: session.serverName,
        lastActivity: session.lastActivity,
        expiresAt: session.expiresAt,
      };
    } catch (error) {
      this.logger.error(`获取会话信息失败: ${error.message}`, error);
      throw error;
    }
  }

}

// 类型定义
interface ServerInfo {
  id: string;
  name: string;
  status: 'active' | 'maintenance' | 'closed';
  openTime: Date;
  maxPlayers: number;
}

interface CharacterInfo {
  characterId: string;
  name: string;
  level: number;
  serverId: string;
  userId: string;
}

interface CharacterLoginResponse {
  characterToken: string;
  expiresIn: number;
  expiresAt: Date;
  character: CharacterInfo;
  server: ServerInfo;
  session: {
    id: string;
    expiresAt: Date;
  };
}

interface CharacterSessionInfo {
  sessionId: string;
  userId: string;
  characterId: string;
  characterName: string;
  serverId: string;
  serverName: string;
  lastActivity: Date;
  expiresAt: Date;
}
