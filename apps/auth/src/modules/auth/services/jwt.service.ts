import { Injectable, Logger, UnauthorizedException, BadRequestException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { JwtService as NestJwtService } from '@nestjs/jwt';
import * as jwt from 'jsonwebtoken';
import * as crypto from 'crypto';
import Redis from 'ioredis';

export interface JwtPayload {
  sub: string;           // 用户ID
  username: string;      // 用户名
  email: string;         // 邮箱
  roles: string[];       // 角色列表
  permissions: string[]; // 权限列表
  sessionId: string;     // 会话ID
  deviceId?: string;     // 设备ID
  type?: string;         // 令牌类型
  scope?: 'account';     // Token作用域（账号级Token）
  iat?: number;          // 签发时间
  exp?: number;          // 过期时间
  jti?: string;          // JWT ID
}

// 新增：角色级Token载荷接口（符合设计文档规范）
export interface CharacterTokenPayload {
  sub: string;              // 账号ID
  username: string;         // 用户名
  email: string;           // 邮箱
  roles: string[];         // 账号级权限
  permissions: string[];   // 权限列表
  characterId: string;     // 角色ID
  serverId: string;        // 区服ID
  sessionId: string;       // 会话ID
  deviceId?: string;       // 设备ID
  scope: 'character';      // Token作用域（符合设计文档）
}

// 新增：角色级JWT载荷接口（包含JWT标准字段）
export interface CharacterJwtPayload extends CharacterTokenPayload {
  jti: string;           // JWT ID
  iat: number;           // 签发时间
  exp: number;           // 过期时间
}

export interface RefreshTokenPayload {
  sub: string;           // 用户ID
  sessionId: string;     // 会话ID
  deviceId?: string;     // 设备ID
  tokenFamily: string;   // 令牌族
  iat?: number;          // 签发时间
  exp?: number;          // 过期时间
  jti?: string;          // JWT ID
}

export interface TokenPair {
  accessToken: string;
  refreshToken: string;
  tokenType: string;
  expiresIn: number;
  expiresAt: Date;
}

// 新增：角色级Token对接口
export interface CharacterTokenPair {
  characterToken: string;
  tokenType: 'Bearer';
  expiresIn: number;
  expiresAt: Date;
  serverId: string;
  characterId: string;
}

@Injectable()
export class JwtService {
  private readonly logger = new Logger(JwtService.name);
  private redis: Redis;
  
  private readonly jwtSecret: string;
  private readonly jwtAlgorithm: string;
  private readonly jwtIssuer: string;
  private readonly jwtAudience: string;
  private readonly accessTokenTTL: string;
  private readonly refreshTokenTTL: string;
  private readonly clockTolerance: number;
  private readonly enableTokenRotation: boolean;
  private readonly blacklistEnabled: boolean;
  private readonly blacklistTTL: number;
  private readonly blacklistKeyPrefix: string;

  // 新增：角色级Token配置
  private readonly characterJwtSecret: string;
  private readonly characterTokenTTL: string;
  private readonly characterJwtIssuer: string;
  private readonly characterJwtAudience: string;

  constructor(
    private configService: ConfigService,
    private nestJwtService: NestJwtService,
  ) {
    this.jwtSecret = this.configService.get<string>('auth.jwt.secret');
    this.jwtAlgorithm = this.configService.get<string>('auth.jwt.algorithm');
    this.jwtIssuer = this.configService.get<string>('auth.jwt.issuer');
    this.jwtAudience = this.configService.get<string>('auth.jwt.audience');
    this.accessTokenTTL = this.configService.get<string>('auth.jwt.accessTokenTTL');
    this.refreshTokenTTL = this.configService.get<string>('auth.jwt.refreshTokenTTL');
    this.clockTolerance = this.configService.get<number>('auth.jwt.clockTolerance');
    this.enableTokenRotation = this.configService.get<boolean>('auth.jwt.enableTokenRotation');
    this.blacklistEnabled = this.configService.get<boolean>('auth.jwt.blacklist.enabled');
    this.blacklistTTL = this.configService.get<number>('auth.jwt.blacklist.ttl');
    this.blacklistKeyPrefix = this.configService.get<string>('auth.jwt.blacklist.keyPrefix');

    // 新增：角色级Token配置初始化（符合设计文档规范）
    this.characterJwtSecret = this.configService.get<string>('auth.jwt.character.secret') || this.jwtSecret;
    this.characterTokenTTL = this.configService.get<string>('auth.jwt.character.expiresIn') || '4h';
    this.characterJwtIssuer = this.configService.get<string>('auth.jwt.character.issuer') || this.jwtIssuer;
    this.characterJwtAudience = this.configService.get<string>('auth.jwt.character.audience') || this.jwtAudience;

    // 调试日志：确认密钥配置
    this.logger.debug(`JWT密钥配置: 普通Token=${this.jwtSecret?.substring(0, 8)}***, 角色Token=${this.characterJwtSecret?.substring(0, 8)}***`);
    this.logger.debug(`JWT Issuer配置: 普通Token=${this.jwtIssuer}, 角色Token=${this.characterJwtIssuer}`);
    this.logger.debug(`JWT Audience配置: 普通Token=${this.jwtAudience}, 角色Token=${this.characterJwtAudience}`);

    this.initializeRedis();
  }

  private initializeRedis(): void {
    try {
      this.redis = new Redis({
        host: this.configService.get<string>('redis.host'),
        port: this.configService.get<number>('redis.port'),
        password: this.configService.get<string>('redis.password'),
        db: this.configService.get<number>('redis.blacklist.db', 2),
        maxRetriesPerRequest: 3,
        lazyConnect: true,
      });
    } catch (error) {
      this.logger.error('Redis初始化失败', error);
    }
  }

  /**
   * 生成访问令牌
   */
  generateAccessToken(payload: Omit<JwtPayload, 'iat' | 'exp' | 'jti'>): string {
    const jti = this.generateTokenId();
    const now = Math.floor(Date.now() / 1000);

    const tokenPayload: JwtPayload = {
      ...payload,
      type: 'access',  // 添加Token类型标识
      scope: 'account', // 添加Token作用域标识
      iat: now,
      jti,
    };

    return jwt.sign(tokenPayload, this.jwtSecret, {
      algorithm: this.jwtAlgorithm as jwt.Algorithm,
      expiresIn: this.accessTokenTTL,
      issuer: this.jwtIssuer,
      audience: this.jwtAudience,
    });
  }

  /**
   * 生成刷新令牌
   */
  generateRefreshToken(payload: Omit<RefreshTokenPayload, 'iat' | 'exp' | 'jti'>): string {
    const jti = this.generateTokenId();
    const now = Math.floor(Date.now() / 1000);
    
    const tokenPayload: RefreshTokenPayload = {
      ...payload,
      iat: now,
      jti,
    };

    return jwt.sign(tokenPayload, this.jwtSecret, {
      algorithm: this.jwtAlgorithm as jwt.Algorithm,
      expiresIn: this.refreshTokenTTL,
      issuer: this.jwtIssuer,
      audience: this.jwtAudience,
    });
  }

  /**
   * 生成令牌对
   */
  generateTokenPair(userPayload: Omit<JwtPayload, 'iat' | 'exp' | 'jti'>): TokenPair {
    const sessionId = userPayload.sessionId;
    const tokenFamily = this.generateTokenFamily();
    
    const accessToken = this.generateAccessToken(userPayload);
    const refreshToken = this.generateRefreshToken({
      sub: userPayload.sub,
      sessionId,
      deviceId: userPayload.deviceId,
      tokenFamily,
    });

    // 计算过期时间
    const expiresIn = this.parseTimeToSeconds(this.accessTokenTTL);
    const expiresAt = new Date(Date.now() + expiresIn * 1000);

    return {
      accessToken,
      refreshToken,
      tokenType: 'Bearer',
      expiresIn,
      expiresAt,
    };
  }

  /**
   * 验证访问令牌（增强版）
   */
  async verifyAccessToken(token: string): Promise<JwtPayload> {
    try {
      // 1. 基础验证
      if (!token || typeof token !== 'string') {
        throw new UnauthorizedException('令牌不能为空');
      }

      // 2. 格式验证
      const parts = token.split('.');
      if (parts.length !== 3) {
        throw new UnauthorizedException('令牌格式不正确');
      }

      // 3. 检查令牌是否在黑名单中
      if (this.blacklistEnabled && await this.isTokenBlacklisted(token)) {
        throw new UnauthorizedException('令牌已被撤销');
      }

      // 4. 验证JWT签名和内容
      const payload = jwt.verify(token, this.jwtSecret, {
        algorithms: [this.jwtAlgorithm as jwt.Algorithm],
        issuer: this.jwtIssuer,
        audience: this.jwtAudience,
        clockTolerance: this.clockTolerance,
        // 严格验证选项
        complete: false,
        ignoreExpiration: false,
        ignoreNotBefore: false,
      }) as JwtPayload;

      // 5. 额外的载荷验证
      this.validateTokenPayload(payload);

      return payload;
    } catch (error) {
      this.logger.error('访问令牌验证失败', error);

      if (error.name === 'TokenExpiredError') {
        throw new UnauthorizedException('令牌已过期');
      } else if (error.name === 'JsonWebTokenError') {
        throw new UnauthorizedException('无效的令牌');
      } else if (error.name === 'NotBeforeError') {
        throw new UnauthorizedException('令牌尚未生效');
      }

      throw new UnauthorizedException('令牌验证失败');
    }
  }

  /**
   * 验证令牌载荷
   */
  private validateTokenPayload(payload: JwtPayload): void {
    // 验证必需字段
    if (!payload.sub) {
      throw new UnauthorizedException('令牌缺少用户标识');
    }

    if (!payload.jti) {
      throw new UnauthorizedException('令牌缺少唯一标识');
    }

    // 验证令牌类型
    if (payload.type && payload.type !== 'access') {
      throw new UnauthorizedException('令牌类型错误');
    }

    // 验证时间戳
    const now = Math.floor(Date.now() / 1000);
    if (payload.iat && payload.iat > now + this.clockTolerance) {
      throw new UnauthorizedException('令牌签发时间无效');
    }

    // 验证过期时间
    if (payload.exp && payload.exp <= now - this.clockTolerance) {
      throw new UnauthorizedException('令牌已过期');
    }
  }

  /**
   * 验证刷新令牌
   */
  async verifyRefreshToken(token: string): Promise<RefreshTokenPayload> {
    try {
      // 检查令牌是否在黑名单中
      if (this.blacklistEnabled && await this.isTokenBlacklisted(token)) {
        throw new UnauthorizedException('刷新令牌已被撤销');
      }

      const payload = jwt.verify(token, this.jwtSecret, {
        algorithms: [this.jwtAlgorithm as jwt.Algorithm],
        issuer: this.jwtIssuer,
        audience: this.jwtAudience,
        clockTolerance: this.clockTolerance,
      }) as RefreshTokenPayload;

      return payload;
    } catch (error) {
      this.logger.error('刷新令牌验证失败', error);
      
      if (error.name === 'TokenExpiredError') {
        throw new UnauthorizedException('刷新令牌已过期');
      } else if (error.name === 'JsonWebTokenError') {
        throw new UnauthorizedException('无效的刷新令牌');
      }
      
      throw new UnauthorizedException('刷新令牌验证失败');
    }
  }

  /**
   * 解码令牌（不验证）
   */
  decodeToken(token: string): any {
    try {
      return jwt.decode(token, { complete: true });
    } catch (error) {
      this.logger.error('令牌解码失败', error);
      return null;
    }
  }

  /**
   * 撤销令牌（加入黑名单）
   */
  async revokeToken(token: string): Promise<void> {
    if (!this.blacklistEnabled || !this.redis) {
      return;
    }

    try {
      const decoded = this.decodeToken(token);
      if (!decoded || !decoded.payload) {
        return;
      }

      const { jti, exp } = decoded.payload;
      if (!jti || !exp) {
        return;
      }

      // 计算剩余TTL
      const now = Math.floor(Date.now() / 1000);
      const ttl = Math.max(0, exp - now);

      if (ttl > 0) {
        const key = `${this.blacklistKeyPrefix}${jti}`;
        await this.redis.setex(key, ttl, '1');
        this.logger.log(`令牌已加入黑名单: ${jti}`);
      }
    } catch (error) {
      this.logger.error('撤销令牌失败', error);
    }
  }

  /**
   * 撤销用户所有令牌
   */
  async revokeAllUserTokens(userId: string): Promise<void> {
    if (!this.blacklistEnabled || !this.redis) {
      return;
    }

    try {
      // 这里可以实现更复杂的逻辑，比如维护用户令牌列表
      // 目前简单地记录撤销时间
      const key = `${this.blacklistKeyPrefix}user:${userId}:revoked_at`;
      await this.redis.set(key, Date.now(), 'EX', this.blacklistTTL);
      
      this.logger.log(`用户所有令牌已撤销: ${userId}`);
    } catch (error) {
      this.logger.error('撤销用户所有令牌失败', error);
    }
  }

  /**
   * 检查令牌是否在黑名单中
   */
  async isTokenBlacklisted(token: string): Promise<boolean> {
    if (!this.blacklistEnabled || !this.redis) {
      return false;
    }

    try {
      const decoded = this.decodeToken(token);
      if (!decoded || !decoded.payload) {
        return false;
      }

      const { jti, sub, iat } = decoded.payload;
      
      // 检查特定令牌是否被撤销
      if (jti) {
        const tokenKey = `${this.blacklistKeyPrefix}${jti}`;
        const isRevoked = await this.redis.exists(tokenKey);
        if (isRevoked) {
          return true;
        }
      }

      // 检查用户所有令牌是否被撤销
      if (sub && iat) {
        const userKey = `${this.blacklistKeyPrefix}user:${sub}:revoked_at`;
        const revokedAt = await this.redis.get(userKey);
        if (revokedAt && parseInt(revokedAt) > iat * 1000) {
          return true;
        }
      }

      return false;
    } catch (error) {
      this.logger.error('检查令牌黑名单失败', error);
      return false;
    }
  }

  /**
   * 生成令牌ID
   */
  private generateTokenId(): string {
    return crypto.randomBytes(16).toString('hex');
  }

  /**
   * 生成令牌族ID
   */
  private generateTokenFamily(): string {
    return crypto.randomBytes(16).toString('hex');
  }

  /**
   * 解析时间字符串为秒数
   */
  private parseTimeToSeconds(timeStr: string): number {
    const match = timeStr.match(/^(\d+)([smhd])$/);
    if (!match) {
      throw new Error(`无效的时间格式: ${timeStr}`);
    }

    const value = parseInt(match[1]);
    const unit = match[2];

    switch (unit) {
      case 's': return value;
      case 'm': return value * 60;
      case 'h': return value * 60 * 60;
      case 'd': return value * 60 * 60 * 24;
      default: throw new Error(`不支持的时间单位: ${unit}`);
    }
  }

  /**
   * 新增：生成角色级访问令牌（高质量实现）
   * 基于现有generateAccessToken方法的完整实现标准
   */
  generateCharacterToken(payload: Omit<CharacterTokenPayload, 'iat' | 'exp' | 'jti'>): string {
    // 1. 验证输入参数
    this.validateCharacterTokenInput(payload);

    // 2. 生成令牌标识和时间戳
    const jti = this.generateTokenId();
    const now = Math.floor(Date.now() / 1000);
    const exp = now + this.parseTimeToSeconds(this.characterTokenTTL);

    // 3. 构建完整的令牌载荷（符合设计文档规范）
    const tokenPayload: CharacterJwtPayload = {
      ...payload,
      iat: now,
      exp,
      jti,
      scope: 'character', // 符合设计文档规范
    };

    // 4. 记录令牌生成日志
    this.logger.debug('生成角色令牌', {
      userId: payload.sub,
      characterId: payload.characterId,
      serverId: payload.serverId,
      sessionId: payload.sessionId,
      jti,
    });

    // 5. 签名并返回令牌（不设置expiresIn，因为payload中已有exp字段）
    this.logger.debug(`生成角色Token: 使用密钥=${this.characterJwtSecret?.substring(0, 8)}***, 算法=${this.jwtAlgorithm}, issuer=${this.characterJwtIssuer}, audience=${this.characterJwtAudience}`);

    const token = jwt.sign(tokenPayload, this.characterJwtSecret, {
      algorithm: this.jwtAlgorithm as jwt.Algorithm,
      issuer: this.characterJwtIssuer,
      audience: this.characterJwtAudience,
    });

    // 调试：解码Token查看实际内容
    const decoded = jwt.decode(token, { complete: true });
    this.logger.debug(`生成的角色Token内容:`, {
      header: decoded?.header,
      payload: decoded?.payload,
    });

    return token;
  }

  /**
   * 新增：验证角色级Token（高质量实现）
   * 基于现有verifyAccessToken的完整验证逻辑
   */
  async verifyCharacterToken(token: string): Promise<CharacterJwtPayload> {
    try {
      // 1. 基础验证
      if (!token || typeof token !== 'string') {
        throw new UnauthorizedException('角色令牌不能为空');
      }

      // 2. 格式验证
      const parts = token.split('.');
      if (parts.length !== 3) {
        throw new UnauthorizedException('角色令牌格式不正确');
      }

      // 3. 检查令牌是否在黑名单中
      if (this.blacklistEnabled && await this.isTokenBlacklisted(token)) {
        throw new UnauthorizedException('角色令牌已被撤销');
      }

      // 4. 验证JWT签名和内容
      this.logger.debug(`验证角色Token: 使用密钥=${this.characterJwtSecret?.substring(0, 8)}***, 算法=${this.jwtAlgorithm}, issuer=${this.characterJwtIssuer}, audience=${this.characterJwtAudience}`);

      const payload = jwt.verify(token, this.characterJwtSecret, {
        algorithms: [this.jwtAlgorithm as jwt.Algorithm],
        issuer: this.characterJwtIssuer,
        audience: this.characterJwtAudience,
        clockTolerance: this.clockTolerance,
        // 严格验证选项
        complete: false,
        ignoreExpiration: false,
        ignoreNotBefore: false,
      }) as CharacterJwtPayload;

      // 5. 角色Token特定验证
      this.validateCharacterTokenPayload(payload);

      // 6. 权限边界验证
      this.validateTokenScope(payload, 'character');

      return payload;
    } catch (error) {
      this.logger.error('角色令牌验证失败', error);

      if (error.name === 'TokenExpiredError') {
        throw new UnauthorizedException('角色令牌已过期');
      } else if (error.name === 'JsonWebTokenError') {
        throw new UnauthorizedException('无效的角色令牌');
      } else if (error.name === 'NotBeforeError') {
        throw new UnauthorizedException('角色令牌尚未生效');
      }

      throw new UnauthorizedException('角色令牌验证失败');
    }
  }

  /**
   * 新增：生成完整的角色认证Token对
   * 基于现有generateTokenPair方法扩展
   */
  generateCharacterTokenPair(
    accountPayload: JwtPayload,
    characterData: { characterId: string; serverId: string; sessionId: string }
  ): CharacterTokenPair {
    const characterPayload: CharacterTokenPayload = {
      sub: accountPayload.sub,
      username: accountPayload.username,
      email: accountPayload.email,
      roles: accountPayload.roles,
      permissions: accountPayload.permissions,
      sessionId: characterData.sessionId,
      deviceId: accountPayload.deviceId,
      characterId: characterData.characterId,
      serverId: characterData.serverId,
      scope: 'character',
    };

    const characterToken = this.generateCharacterToken(characterPayload);

    // 计算过期时间
    const expiresIn = this.parseTimeToSeconds(this.characterTokenTTL);
    const expiresAt = new Date(Date.now() + expiresIn * 1000);

    return {
      characterToken,
      tokenType: 'Bearer',
      expiresIn,
      expiresAt,
      serverId: characterData.serverId,
      characterId: characterData.characterId,
    };
  }

  /**
   * 新增：验证角色Token输入参数
   * 确保生成角色Token时的输入数据完整性
   */
  private validateCharacterTokenInput(payload: Omit<CharacterTokenPayload, 'iat' | 'exp' | 'jti'>): void {
    if (!payload.sub) {
      throw new BadRequestException('用户标识不能为空');
    }

    if (!payload.username || typeof payload.username !== 'string') {
      throw new BadRequestException('用户名不能为空且必须为字符串');
    }

    if (!payload.email || typeof payload.email !== 'string') {
      throw new BadRequestException('邮箱不能为空且必须为字符串');
    }

    if (!payload.characterId || typeof payload.characterId !== 'string') {
      throw new BadRequestException('角色标识不能为空且必须为字符串');
    }

    if (!payload.serverId || typeof payload.serverId !== 'string') {
      throw new BadRequestException('服务器标识不能为空且必须为字符串');
    }

    if (!payload.sessionId || typeof payload.sessionId !== 'string') {
      throw new BadRequestException('会话标识不能为空且必须为字符串');
    }

    if (!Array.isArray(payload.roles)) {
      throw new BadRequestException('角色列表必须为数组');
    }

    if (!Array.isArray(payload.permissions)) {
      throw new BadRequestException('权限列表必须为数组');
    }

    if (payload.scope && payload.scope !== 'character') {
      throw new BadRequestException('令牌作用域必须为character或未指定');
    }
  }

  /**
   * 新增：验证角色Token载荷
   * 基于现有validateTokenPayload方法扩展
   */
  private validateCharacterTokenPayload(payload: CharacterJwtPayload): void {
    // 验证基础必需字段
    if (!payload.sub) {
      throw new UnauthorizedException('角色令牌缺少用户标识');
    }

    if (!payload.username) {
      throw new UnauthorizedException('角色令牌缺少用户名');
    }

    if (!payload.email) {
      throw new UnauthorizedException('角色令牌缺少邮箱');
    }

    if (!payload.sessionId) {
      throw new UnauthorizedException('角色令牌缺少会话标识');
    }

    // 验证角色Token特有字段
    if (!payload.characterId) {
      throw new UnauthorizedException('角色令牌缺少角色标识');
    }

    if (!payload.serverId) {
      throw new UnauthorizedException('角色令牌缺少服务器标识');
    }

    if (payload.scope !== 'character') {
      throw new UnauthorizedException('令牌作用域不正确，期望角色令牌');
    }

    // 验证数组字段
    if (!Array.isArray(payload.roles)) {
      throw new UnauthorizedException('角色令牌角色列表格式不正确');
    }

    if (!Array.isArray(payload.permissions)) {
      throw new UnauthorizedException('角色令牌权限列表格式不正确');
    }

    // 验证时间戳
    if (!payload.iat || typeof payload.iat !== 'number') {
      throw new UnauthorizedException('角色令牌签发时间无效');
    }

    // 验证令牌ID
    if (!payload.jti) {
      throw new UnauthorizedException('角色令牌缺少唯一标识');
    }
  }

  /**
   * 新增：验证Token权限边界
   * 确保Token只能在其授权范围内使用
   */
  private validateTokenScope(payload: any, expectedScope: string): void {
    if (!payload.scope) {
      throw new UnauthorizedException('令牌缺少作用域信息');
    }

    if (payload.scope !== expectedScope) {
      throw new UnauthorizedException(`令牌作用域不匹配，期望: ${expectedScope}, 实际: ${payload.scope}`);
    }

    // 验证权限边界
    switch (expectedScope) {
      case 'character':
        // 角色Token必须包含角色和区服信息
        if (!payload.characterId || !payload.serverId) {
          throw new UnauthorizedException('角色令牌缺少必要的角色或区服信息');
        }
        break;
      case 'account':
        // 账号Token不应包含角色信息
        if (payload.characterId || payload.serverId) {
          throw new UnauthorizedException('账号令牌不应包含角色或区服信息');
        }
        break;
      default:
        throw new UnauthorizedException(`不支持的令牌作用域: ${expectedScope}`);
    }
  }

  /**
   * 新增：统一Token验证方法（高质量实现）
   * 自动识别Token类型并验证，支持账号Token和角色Token
   */
  async verifyAnyToken(token: string): Promise<JwtPayload | CharacterJwtPayload> {
    try {
      // 先尝试验证为账号Token
      return await this.verifyAccessToken(token);
    } catch (accountError) {
      try {
        // 再尝试验证为角色Token
        return await this.verifyCharacterToken(token);
      } catch (characterError) {
        this.logger.error('Token验证失败', {
          accountError: accountError.message,
          characterError: characterError.message,
        });
        throw new UnauthorizedException('无效的令牌：既不是账号令牌也不是角色令牌');
      }
    }
  }

  /**
   * 清理资源
   */
  async onModuleDestroy(): Promise<void> {
    if (this.redis) {
      await this.redis.quit();
    }
  }
}
