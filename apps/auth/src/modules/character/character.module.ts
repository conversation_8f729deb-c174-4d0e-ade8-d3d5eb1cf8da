import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { MicroserviceKitModule } from '@common/microservice-kit';
import { CharacterController } from './controllers/character.controller';
import { CharacterService } from './services/character.service';
import { CharacterValidationService } from './services/character-validation.service';
import { CharacterCacheService } from './services/character-cache.service';
import { AuthCharacter, AuthCharacterSchema } from './entities/auth-character.entity';
import { UserCharacterMapping, UserCharacterMappingSchema } from './entities/user-character-mapping.entity';
import { AuthCharacterRepository } from './repositories/auth-character.repository';
import { UserCharacterMappingRepository } from './repositories/user-character-mapping.repository';

/**
 * 角色管理模块
 * 
 * 职责：
 * - 角色ID生成和管理
 * - 角色基础信息CRUD
 * - 角色名称唯一性验证
 * - 用户角色关系管理
 */
@Module({
  imports: [
    MongooseModule.forFeature([
      { name: AuthCharacter.name, schema: AuthCharacterSchema },
      { name: UserCharacterMapping.name, schema: UserCharacterMappingSchema },
    ]),
  ],
  controllers: [
    CharacterController,
  ],
  providers: [
    CharacterService,
    CharacterValidationService,
    CharacterCacheService,
    AuthCharacterRepository,
    UserCharacterMappingRepository,
  ],
  exports: [
    CharacterService,
    CharacterValidationService,
    CharacterCacheService,
  ],
})
export class CharacterModule {}
