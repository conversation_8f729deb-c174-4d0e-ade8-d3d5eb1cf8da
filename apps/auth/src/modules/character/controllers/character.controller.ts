import {
  Controller,
  Get,
  Post,
  Patch,
  Delete,
  Body,
  Param,
  UseGuards,
  Logger,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiBody,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { CharacterService } from '../services/character.service';
import {
  CreateCharacterDto,
  UpdateCharacterDto,
  CharacterResponseDto,
  CharacterDetailResponseDto,
} from '../dto/create-character.dto';

/**
 * 角色管理控制器
 * 
 * 提供角色管理相关的HTTP接口
 * 所有接口都需要用户登录
 */
@ApiTags('角色管理')
@Controller('characters')
// @UseGuards(AuthGuard) // TODO: 添加认证守卫
@ApiBearerAuth()
export class CharacterController {
  private readonly logger = new Logger(CharacterController.name);

  constructor(private readonly characterService: CharacterService) {}

  /**
   * 创建角色
   */
  @Post()
  @ApiOperation({ summary: '创建角色' })
  @ApiBody({ type: CreateCharacterDto })
  @ApiResponse({ 
    status: 201, 
    description: '角色创建成功',
    type: CharacterResponseDto 
  })
  @ApiResponse({ 
    status: 409, 
    description: '角色已存在或名称冲突' 
  })
  async createCharacter(
    @Body() createDto: CreateCharacterDto,
    // 注意：在生产环境中，应该使用@CurrentUser()装饰器获取认证用户
    // 这里为了测试方便，从请求体中获取userId
  ): Promise<CharacterResponseDto> {
    this.logger.log(`📝 创建角色请求: serverId=${createDto.serverId}, name=${createDto.characterName}`);

    // 从请求体中获取userId（生产环境应该从认证用户中获取）
    const userId = createDto.userId || 'test_user_001';
    
    return await this.characterService.createCharacter(userId, createDto);
  }

  /**
   * 获取用户的所有角色
   */
  @Get()
  @ApiOperation({ summary: '获取用户的所有角色' })
  @ApiResponse({ 
    status: 200, 
    description: '获取成功',
    type: [CharacterResponseDto] 
  })
  async getUserCharacters(
    // @CurrentUser() user: User, // TODO: 添加用户装饰器
  ): Promise<CharacterResponseDto[]> {
    this.logger.log(`📋 获取用户所有角色请求`);
    
    // TODO: 从认证用户中获取userId
    const userId = 'temp_user_id'; // 临时实现
    
    return await this.characterService.getUserAllCharacters(userId);
  }

  /**
   * 获取用户在指定区服的角色
   */
  @Get('server/:serverId')
  @ApiOperation({ summary: '获取用户在指定区服的角色' })
  @ApiParam({ name: 'serverId', description: '区服ID', example: 'server001' })
  @ApiResponse({ 
    status: 200, 
    description: '获取成功',
    type: CharacterResponseDto 
  })
  @ApiResponse({ 
    status: 404, 
    description: '角色不存在' 
  })
  async getUserCharacterInServer(
    // @CurrentUser() user: User, // TODO: 添加用户装饰器
    @Param('serverId') serverId: string,
  ): Promise<CharacterResponseDto | null> {
    this.logger.log(`🎮 获取用户在区服${serverId}的角色`);
    
    // TODO: 从认证用户中获取userId
    const userId = 'temp_user_id'; // 临时实现
    
    return await this.characterService.getUserCharacterInServer(userId, serverId);
  }

  /**
   * 获取角色详细信息
   */
  @Get(':characterId')
  @ApiOperation({ summary: '获取角色详细信息' })
  @ApiParam({ name: 'characterId', description: '角色ID' })
  @ApiResponse({ 
    status: 200, 
    description: '获取成功',
    type: CharacterDetailResponseDto 
  })
  @ApiResponse({ 
    status: 404, 
    description: '角色不存在' 
  })
  @ApiResponse({ 
    status: 403, 
    description: '无权限访问该角色' 
  })
  async getCharacterDetail(
    // @CurrentUser() user: User, // TODO: 添加用户装饰器
    @Param('characterId') characterId: string,
  ): Promise<CharacterDetailResponseDto> {
    this.logger.log(`🔍 获取角色详细信息: ${characterId}`);
    
    // TODO: 从认证用户中获取userId
    const userId = 'temp_user_id'; // 临时实现
    
    // 验证角色归属
    await this.characterService.validateCharacterOwnership(userId, characterId);
    
    return await this.characterService.getCharacterDetail(characterId);
  }

  /**
   * 更新角色基础信息
   */
  @Patch(':characterId')
  @ApiOperation({ summary: '更新角色基础信息' })
  @ApiParam({ name: 'characterId', description: '角色ID' })
  @ApiBody({ type: UpdateCharacterDto })
  @ApiResponse({ 
    status: 200, 
    description: '更新成功',
    type: CharacterResponseDto 
  })
  @ApiResponse({ 
    status: 404, 
    description: '角色不存在' 
  })
  @ApiResponse({ 
    status: 403, 
    description: '无权限修改该角色' 
  })
  async updateCharacter(
    // @CurrentUser() user: User, // TODO: 添加用户装饰器
    @Param('characterId') characterId: string,
    @Body() updateDto: UpdateCharacterDto,
  ): Promise<CharacterResponseDto> {
    this.logger.log(`✏️ 更新角色信息: ${characterId}`);
    
    // TODO: 从认证用户中获取userId
    const userId = 'temp_user_id'; // 临时实现
    
    // 验证角色归属
    await this.characterService.validateCharacterOwnership(userId, characterId);
    
    return await this.characterService.updateCharacter(characterId, updateDto);
  }

  /**
   * 删除角色（软删除）
   */
  @Delete(':characterId')
  @ApiOperation({ summary: '删除角色' })
  @ApiParam({ name: 'characterId', description: '角色ID' })
  @ApiResponse({ 
    status: 200, 
    description: '删除成功' 
  })
  @ApiResponse({ 
    status: 404, 
    description: '角色不存在' 
  })
  @ApiResponse({ 
    status: 403, 
    description: '无权限删除该角色' 
  })
  @ApiResponse({ 
    status: 409, 
    description: '角色无法删除' 
  })
  async deleteCharacter(
    // @CurrentUser() user: User, // TODO: 添加用户装饰器
    @Param('characterId') characterId: string,
  ): Promise<{ success: boolean; message: string }> {
    this.logger.log(`🗑️ 删除角色: ${characterId}`);
    
    // TODO: 从认证用户中获取userId
    const userId = 'temp_user_id'; // 临时实现
    
    // 验证角色归属
    await this.characterService.validateCharacterOwnership(userId, characterId);
    
    await this.characterService.softDeleteCharacter(characterId);
    
    return { 
      success: true, 
      message: '角色删除成功' 
    };
  }
}
