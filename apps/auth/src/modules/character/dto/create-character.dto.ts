import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, Length, IsOptional, IsNumber, Min, Max } from 'class-validator';

/**
 * 创建角色请求DTO
 */
export class CreateCharacterDto {
  @ApiProperty({
    description: '用户ID（测试用，生产环境从认证用户获取）',
    example: 'test_user_001',
    required: false,
  })
  @IsOptional()
  @IsString()
  userId?: string;

  @ApiProperty({
    description: '区服ID',
    example: 'server001',
    minLength: 1,
    maxLength: 32,
  })
  @IsString()
  @IsNotEmpty()
  @Length(1, 32)
  serverId: string;

  @ApiProperty({
    description: '角色名称',
    example: 'PlayerName123',
    minLength: 2,
    maxLength: 50,
  })
  @IsString()
  @IsNotEmpty()
  @Length(2, 50)
  characterName: string;

  @ApiProperty({
    description: '显示名称（可选）',
    example: '玩家昵称',
    required: false,
    minLength: 2,
    maxLength: 50,
  })
  @IsOptional()
  @IsString()
  @Length(2, 50)
  displayName?: string;

  @ApiProperty({
    description: '头像图标ID',
    example: 15,
    minimum: 1,
    maximum: 100,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(100)
  faceIcon?: number;

  @ApiProperty({
    description: '头像URL',
    example: 'default_avatar.png',
    required: false,
  })
  @IsOptional()
  @IsString()
  avatar?: string;
}

/**
 * 更新角色请求DTO
 */
export class UpdateCharacterDto {
  @ApiProperty({
    description: '显示名称',
    example: '新的昵称',
    required: false,
    minLength: 2,
    maxLength: 50,
  })
  @IsOptional()
  @IsString()
  @Length(2, 50)
  displayName?: string;

  @ApiProperty({
    description: '头像图标ID',
    example: 20,
    minimum: 1,
    maximum: 100,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(100)
  faceIcon?: number;

  @ApiProperty({
    description: '头像URL',
    example: 'new_avatar.png',
    required: false,
  })
  @IsOptional()
  @IsString()
  avatar?: string;
}

/**
 * 角色响应DTO
 */
export class CharacterResponseDto {
  @ApiProperty({
    description: '角色ID',
    example: 'char_server001_abc123_def456',
  })
  characterId: string;

  @ApiProperty({
    description: '用户ID',
    example: 'user_123456',
  })
  userId: string;

  @ApiProperty({
    description: '区服ID',
    example: 'server001',
  })
  serverId: string;

  @ApiProperty({
    description: '角色名称',
    example: 'PlayerName123',
  })
  characterName: string;

  @ApiProperty({
    description: '显示名称',
    example: '玩家昵称',
    required: false,
  })
  displayName?: string;

  @ApiProperty({
    description: '角色状态',
    example: 'active',
    enum: ['active', 'inactive', 'banned', 'deleted'],
  })
  status: 'active' | 'inactive' | 'banned' | 'deleted';

  @ApiProperty({
    description: '创建时间',
    example: '2024-12-19T10:00:00Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: '最后登录时间',
    example: '2024-12-19T15:30:00Z',
  })
  lastLoginAt: Date;

  @ApiProperty({
    description: '登录次数',
    example: 42,
  })
  loginCount: number;

  @ApiProperty({
    description: '扩展信息',
    example: { level: 10, avatar: 'avatar.png', vipLevel: 1 },
    required: false,
  })
  metadata?: {
    level?: number;
    avatar?: string;
    vipLevel?: number;
    faceIcon?: number;
    [key: string]: any;
  };
}

/**
 * 角色详细信息响应DTO
 */
export class CharacterDetailResponseDto extends CharacterResponseDto {
  @ApiProperty({
    description: '总游戏时长（秒）',
    example: 86400,
  })
  totalPlayTime: number;

  @ApiProperty({
    description: '封禁原因',
    example: '违规行为',
    required: false,
  })
  banReason?: string;

  @ApiProperty({
    description: '封禁到期时间',
    example: '2024-12-25T00:00:00Z',
    required: false,
  })
  banExpiresAt?: Date;
}
