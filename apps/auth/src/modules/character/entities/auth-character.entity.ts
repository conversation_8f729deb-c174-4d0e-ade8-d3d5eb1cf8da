import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';
import { ApiProperty } from '@nestjs/swagger';

/**
 * 角色基础信息实体
 *
 * 设计理念：
 * - 全局唯一性：角色ID在整个系统中全局唯一
 * - 业务约束：一个用户在一个区服只能有一个角色
 * - 状态管理：支持角色的完整生命周期管理
 * - 扩展性：使用嵌套对象支持灵活的扩展信息
 */
@Schema({
  timestamps: true,
  collection: 'auth_characters',
})
export class AuthCharacter {
  @ApiProperty({ description: '角色ID' })
  id?: string;

  /**
   * 角色ID - 全局唯一标识符
   * 格式：char_{server_id}_{hash}_{random}
   */
  @ApiProperty({ description: '角色ID，全局唯一标识符' })
  @Prop({
    required: true,
    unique: true,
    index: true,
    comment: '角色ID，全局唯一标识符'
  })
  characterId: string;

  /**
   * 用户ID - 关联用户表
   */
  @ApiProperty({ description: '用户ID，关联用户表' })
  @Prop({
    required: true,
    index: true,
    comment: '用户ID，关联用户表'
  })
  userId: string;

  /**
   * 区服ID - 标识角色所属区服
   */
  @ApiProperty({ description: '区服ID，标识角色所属区服' })
  @Prop({
    required: true,
    index: true,
    comment: '区服ID，标识角色所属区服'
  })
  serverId: string;

  /**
   * 角色名称 - 区服内唯一
   */
  @ApiProperty({ description: '角色名称，区服内唯一' })
  @Prop({
    required: true,
    index: true,
    comment: '角色名称，区服内唯一'
  })
  characterName: string;

  /**
   * 显示名称 - 支持特殊字符和表情
   */
  @ApiProperty({ description: '显示名称，支持特殊字符' })
  @Prop({
    required: false,
    comment: '显示名称，支持特殊字符'
  })
  displayName?: string;

  /**
   * 角色状态
   */
  @ApiProperty({
    description: '角色状态',
    enum: ['active', 'inactive', 'banned', 'deleted']
  })
  @Prop({
    required: true,
    enum: ['active', 'inactive', 'banned', 'deleted'],
    default: 'active',
    index: true,
    comment: '角色状态',
  })
  status: 'active' | 'inactive' | 'banned' | 'deleted';

  /**
   * 封禁原因
   */
  @ApiProperty({ description: '封禁原因' })
  @Prop({
    required: false,
    comment: '封禁原因'
  })
  banReason?: string;

  /**
   * 封禁到期时间
   */
  @ApiProperty({ description: '封禁到期时间' })
  @Prop({
    required: false,
    comment: '封禁到期时间'
  })
  banExpiresAt?: Date;

  /**
   * 最后登录时间
   */
  @ApiProperty({ description: '最后登录时间' })
  @Prop({
    required: true,
    default: Date.now,
    index: true,
    comment: '最后登录时间'
  })
  lastLoginAt: Date;

  /**
   * 软删除时间
   */
  @ApiProperty({ description: '软删除时间' })
  @Prop({
    required: false,
    comment: '软删除时间'
  })
  deletedAt?: Date;

  /**
   * 登录次数
   */
  @ApiProperty({ description: '登录次数' })
  @Prop({
    required: true,
    default: 0,
    comment: '登录次数'
  })
  loginCount: number;

  /**
   * 总游戏时长（秒）
   */
  @ApiProperty({ description: '总游戏时长（秒）' })
  @Prop({
    required: true,
    default: 0,
    comment: '总游戏时长（秒）'
  })
  totalPlayTime: number;

  /**
   * 扩展信息 - 嵌套对象存储
   * 示例：{"level":1,"avatar":"default.png","vipLevel":0,"faceIcon":15}
   */
  @ApiProperty({
    description: '扩展信息',
    example: { level: 1, avatar: 'default.png', vipLevel: 0, faceIcon: 15 }
  })
  @Prop({
    required: false,
    type: Object,
    comment: '扩展信息：{"level":1,"avatar":"default.png","vipLevel":0}',
  })
  metadata?: {
    level?: number;
    avatar?: string;
    vipLevel?: number;
    faceIcon?: number;
    lastActiveServer?: string;
    createdFrom?: string;
    [key: string]: any;
  };

  @ApiProperty({ description: '创建时间' })
  createdAt?: Date;

  @ApiProperty({ description: '更新时间' })
  updatedAt?: Date;
}

export const AuthCharacterSchema = SchemaFactory.createForClass(AuthCharacter);

// 创建复合索引 - 核心业务约束
AuthCharacterSchema.index({ userId: 1, serverId: 1 }, { unique: true, name: 'uk_user_server' });
AuthCharacterSchema.index({ serverId: 1, characterName: 1 }, { unique: true, name: 'uk_server_name' });
AuthCharacterSchema.index({ characterId: 1 }, { unique: true, name: 'uk_character_id' });
AuthCharacterSchema.index({ userId: 1 });
AuthCharacterSchema.index({ serverId: 1 });
AuthCharacterSchema.index({ status: 1 });
AuthCharacterSchema.index({ lastLoginAt: 1 });
AuthCharacterSchema.index({ createdAt: 1 });

// 虚拟字段
AuthCharacterSchema.virtual('id').get(function() {
  return this._id.toHexString();
});

// 确保虚拟字段被序列化
AuthCharacterSchema.set('toJSON', {
  virtuals: true,
  transform: function(doc, ret) {
    delete ret._id;
    delete ret.__v;
    return ret;
  },
});

// 中间件：保存前验证
AuthCharacterSchema.pre('save', function(next) {
  // 验证角色名称格式
  if (this.characterName && (this.characterName.length < 2 || this.characterName.length > 50)) {
    next(new Error('角色名称长度必须在2-50个字符之间'));
    return;
  }

  // 验证区服ID格式
  if (this.serverId && !/^server\d{3}$/.test(this.serverId)) {
    next(new Error('区服ID格式错误，应为server001格式'));
    return;
  }

  // 确保角色ID格式正确
  if (this.characterId && !this.characterId.startsWith('char_')) {
    next(new Error('角色ID格式错误'));
    return;
  }

  next();
});

// 静态方法：查找用户在指定区服的角色
AuthCharacterSchema.statics.findByUserAndServer = function(userId: string, serverId: string) {
  return this.findOne({
    userId,
    serverId,
    status: 'active',
  });
};

// 静态方法：检查角色名称是否存在
AuthCharacterSchema.statics.isCharacterNameExists = function(serverId: string, characterName: string) {
  return this.findOne({
    serverId,
    characterName,
    status: 'active',
  });
};

// 静态方法：查找用户的所有角色
AuthCharacterSchema.statics.findByUserId = function(userId: string) {
  return this.find({
    userId,
    status: 'active',
  }).sort({ createdAt: -1 });
};

// 实例方法：更新登录信息
AuthCharacterSchema.methods.updateLoginInfo = function() {
  this.lastLoginAt = new Date();
  this.loginCount += 1;
  return this.save();
};

// 实例方法：软删除
AuthCharacterSchema.methods.softDelete = function() {
  this.status = 'deleted';
  this.deletedAt = new Date();
  return this.save();
};

// 实例方法：检查是否可以删除
AuthCharacterSchema.methods.canDelete = function(): { canDelete: boolean; reason?: string } {
  if (this.status === 'deleted') {
    return {
      canDelete: false,
      reason: '角色已被删除',
    };
  }

  if (this.loginCount > 100) {
    return {
      canDelete: false,
      reason: '角色登录次数过多，不建议删除',
    };
  }

  return { canDelete: true };
};

// 扩展AuthCharacterDocument接口以包含自定义方法
export interface AuthCharacterDocument extends Omit<AuthCharacter, 'id'>, Document {
  updateLoginInfo(): Promise<AuthCharacterDocument>;
  softDelete(): Promise<AuthCharacterDocument>;
  canDelete(): { canDelete: boolean; reason?: string };
}
