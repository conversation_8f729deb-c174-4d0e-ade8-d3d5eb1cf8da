import {
  Entity,
  PrimaryColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from 'typeorm';

/**
 * 角色基础信息实体
 * 
 * 设计理念：
 * - 全局唯一性：角色ID在整个系统中全局唯一
 * - 业务约束：一个用户在一个区服只能有一个角色
 * - 状态管理：支持角色的完整生命周期管理
 * - 扩展性：使用JSON字段支持灵活的扩展信息
 */
@Entity('auth_characters')
@Index('uk_user_server', ['userId', 'serverId'], { unique: true })
@Index('uk_server_name', ['serverId', 'characterName'], { unique: true })
@Index('idx_user_id', ['userId'])
@Index('idx_server_id', ['serverId'])
@Index('idx_status', ['status'])
@Index('idx_last_login', ['lastLoginAt'])
export class AuthCharacter {
  /**
   * 角色ID - 全局唯一标识符
   * 格式：char_{server_id}_{hash}_{random}
   */
  @PrimaryColumn({ type: 'varchar', length: 64, comment: '角色ID，全局唯一标识符' })
  characterId: string;

  /**
   * 用户ID - 关联用户表
   */
  @Column({ type: 'varchar', length: 64, comment: '用户ID，关联用户表' })
  userId: string;

  /**
   * 区服ID - 标识角色所属区服
   */
  @Column({ type: 'varchar', length: 32, comment: '区服ID，标识角色所属区服' })
  serverId: string;

  /**
   * 角色名称 - 区服内唯一
   */
  @Column({ type: 'varchar', length: 50, comment: '角色名称，区服内唯一' })
  characterName: string;

  /**
   * 显示名称 - 支持特殊字符和表情
   */
  @Column({ type: 'varchar', length: 50, nullable: true, comment: '显示名称，支持特殊字符' })
  displayName?: string;

  /**
   * 角色状态
   */
  @Column({
    type: 'enum',
    enum: ['active', 'inactive', 'banned', 'deleted'],
    default: 'active',
    comment: '角色状态',
  })
  status: 'active' | 'inactive' | 'banned' | 'deleted';

  /**
   * 封禁原因
   */
  @Column({ type: 'varchar', length: 255, nullable: true, comment: '封禁原因' })
  banReason?: string;

  /**
   * 封禁到期时间
   */
  @Column({ type: 'timestamp', nullable: true, comment: '封禁到期时间' })
  banExpiresAt?: Date;

  /**
   * 创建时间
   */
  @CreateDateColumn({ comment: '创建时间' })
  createdAt: Date;

  /**
   * 更新时间
   */
  @UpdateDateColumn({ comment: '更新时间' })
  updatedAt: Date;

  /**
   * 最后登录时间
   */
  @Column({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP', comment: '最后登录时间' })
  lastLoginAt: Date;

  /**
   * 软删除时间
   */
  @Column({ type: 'timestamp', nullable: true, comment: '软删除时间' })
  deletedAt?: Date;

  /**
   * 登录次数
   */
  @Column({ type: 'int', default: 0, comment: '登录次数' })
  loginCount: number;

  /**
   * 总游戏时长（秒）
   */
  @Column({ type: 'int', default: 0, comment: '总游戏时长（秒）' })
  totalPlayTime: number;

  /**
   * 扩展信息 - JSON格式存储
   * 示例：{"level":1,"avatar":"default.png","vipLevel":0,"faceIcon":15}
   */
  @Column({
    type: 'json',
    nullable: true,
    comment: '扩展信息：{"level":1,"avatar":"default.png","vipLevel":0}',
  })
  metadata?: {
    level?: number;
    avatar?: string;
    vipLevel?: number;
    faceIcon?: number;
    lastActiveServer?: string;
    createdFrom?: string;
    [key: string]: any;
  };
}
