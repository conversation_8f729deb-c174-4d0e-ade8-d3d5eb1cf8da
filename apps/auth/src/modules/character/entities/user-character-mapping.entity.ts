import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';
import { ApiProperty } from '@nestjs/swagger';

/**
 * 用户角色映射实体
 *
 * 设计理念：
 * - 关系映射：建立用户、区服、角色的三元关系
 * - 数据一致性：通过索引约束确保数据完整性
 * - 扩展性：为未来可能的多角色功能预留空间
 * - 权限控制：支持角色级别的权限管理
 */
@Schema({
  timestamps: true,
  collection: 'user_character_mappings',
})
export class UserCharacterMapping {
  @ApiProperty({ description: '映射ID' })
  id?: string;

  /**
   * 用户ID
   */
  @Column({ type: 'varchar', length: 64, comment: '用户ID' })
  userId: string;

  /**
   * 区服ID
   */
  @Column({ type: 'varchar', length: 32, comment: '区服ID' })
  serverId: string;

  /**
   * 角色ID
   */
  @Column({ type: 'varchar', length: 64, comment: '角色ID' })
  characterId: string;

  /**
   * 是否为主角色（当前业务下总是TRUE）
   */
  @Column({ type: 'boolean', default: true, comment: '是否为主角色（当前业务下总是TRUE）' })
  isPrimary: boolean;

  /**
   * 角色类型：主角色、小号、测试角色
   */
  @Column({
    type: 'enum',
    enum: ['main', 'alt', 'test'],
    default: 'main',
    comment: '角色类型：主角色、小号、测试角色',
  })
  roleType: 'main' | 'alt' | 'test';

  /**
   * 角色特定权限
   * 示例：{"canTrade":true,"canChat":true,"canCreateGuild":false}
   */
  @Column({
    type: 'json',
    nullable: true,
    comment: '角色特定权限：{"canTrade":true,"canChat":true,"canCreateGuild":false}',
  })
  permissions?: {
    canTrade?: boolean;
    canChat?: boolean;
    canCreateGuild?: boolean;
    canParticipateMatch?: boolean;
    [key: string]: any;
  };

  /**
   * 创建时间
   */
  @CreateDateColumn({ comment: '创建时间' })
  createdAt: Date;

  /**
   * 更新时间
   */
  @UpdateDateColumn({ comment: '更新时间' })
  updatedAt: Date;

  /**
   * 最后访问时间
   */
  @Column({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    comment: '最后访问时间',
  })
  lastAccessedAt: Date;

  /**
   * 访问次数
   */
  @Column({ type: 'int', default: 0, comment: '访问次数' })
  accessCount: number;

  /**
   * 关联角色实体
   */
  @OneToOne(() => AuthCharacter, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'character_id' })
  character: AuthCharacter;
}
