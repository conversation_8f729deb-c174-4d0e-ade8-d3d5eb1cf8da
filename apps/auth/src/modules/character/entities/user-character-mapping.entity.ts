import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';
import { ApiProperty } from '@nestjs/swagger';

/**
 * 用户角色映射实体
 *
 * 设计理念：
 * - 关系映射：建立用户、区服、角色的三元关系
 * - 数据一致性：通过索引约束确保数据完整性
 * - 扩展性：为未来可能的多角色功能预留空间
 * - 权限控制：支持角色级别的权限管理
 */
@Schema({
  timestamps: true,
  collection: 'user_character_mappings',
})
export class UserCharacterMapping {
  @ApiProperty({ description: '映射ID' })
  id?: string;

  /**
   * 用户ID
   */
  @ApiProperty({ description: '用户ID' })
  @Prop({
    required: true,
    index: true,
    comment: '用户ID'
  })
  userId: string;

  /**
   * 区服ID
   */
  @ApiProperty({ description: '区服ID' })
  @Prop({
    required: true,
    index: true,
    comment: '区服ID'
  })
  serverId: string;

  /**
   * 角色ID
   */
  @ApiProperty({ description: '角色ID' })
  @Prop({
    required: true,
    index: true,
    comment: '角色ID'
  })
  characterId: string;

  /**
   * 是否为主角色（当前业务下总是TRUE）
   */
  @ApiProperty({ description: '是否为主角色' })
  @Prop({
    required: true,
    default: true,
    comment: '是否为主角色（当前业务下总是TRUE）'
  })
  isPrimary: boolean;

  /**
   * 角色类型：主角色、小号、测试角色
   */
  @ApiProperty({
    description: '角色类型',
    enum: ['main', 'alt', 'test']
  })
  @Prop({
    required: true,
    enum: ['main', 'alt', 'test'],
    default: 'main',
    index: true,
    comment: '角色类型：主角色、小号、测试角色',
  })
  roleType: 'main' | 'alt' | 'test';

  /**
   * 角色特定权限
   * 示例：{"canTrade":true,"canChat":true,"canCreateGuild":false}
   */
  @ApiProperty({
    description: '角色特定权限',
    example: { canTrade: true, canChat: true, canCreateGuild: false }
  })
  @Prop({
    required: false,
    type: Object,
    comment: '角色特定权限：{"canTrade":true,"canChat":true,"canCreateGuild":false}',
  })
  permissions?: {
    canTrade?: boolean;
    canChat?: boolean;
    canCreateGuild?: boolean;
    canParticipateMatch?: boolean;
    [key: string]: any;
  };

  /**
   * 最后访问时间
   */
  @ApiProperty({ description: '最后访问时间' })
  @Prop({
    required: true,
    default: Date.now,
    comment: '最后访问时间',
  })
  lastAccessedAt: Date;

  /**
   * 访问次数
   */
  @ApiProperty({ description: '访问次数' })
  @Prop({
    required: true,
    default: 0,
    comment: '访问次数'
  })
  accessCount: number;

  @ApiProperty({ description: '创建时间' })
  createdAt?: Date;

  @ApiProperty({ description: '更新时间' })
  updatedAt?: Date;
}

export const UserCharacterMappingSchema = SchemaFactory.createForClass(UserCharacterMapping);

// 创建复合索引 - 核心业务约束
UserCharacterMappingSchema.index({ userId: 1, serverId: 1 }, { unique: true, name: 'uk_user_server' });
UserCharacterMappingSchema.index({ characterId: 1 }, { unique: true, name: 'uk_character' });
UserCharacterMappingSchema.index({ userId: 1 });
UserCharacterMappingSchema.index({ serverId: 1 });
UserCharacterMappingSchema.index({ roleType: 1 });

// 虚拟字段
UserCharacterMappingSchema.virtual('id').get(function() {
  return this._id.toHexString();
});

// 确保虚拟字段被序列化
UserCharacterMappingSchema.set('toJSON', {
  virtuals: true,
  transform: function(doc, ret) {
    delete ret._id;
    delete ret.__v;
    return ret;
  },
});

// 静态方法：根据用户ID和区服ID查找映射
UserCharacterMappingSchema.statics.findByUserAndServer = function(userId: string, serverId: string) {
  return this.findOne({
    userId,
    serverId,
  });
};

// 静态方法：根据角色ID查找映射
UserCharacterMappingSchema.statics.findByCharacterId = function(characterId: string) {
  return this.findOne({
    characterId,
  });
};

// 静态方法：根据用户ID查找所有映射
UserCharacterMappingSchema.statics.findByUserId = function(userId: string) {
  return this.find({
    userId,
  }).sort({ createdAt: -1 });
};

// 实例方法：更新最后访问时间
UserCharacterMappingSchema.methods.updateLastAccessed = function() {
  this.lastAccessedAt = new Date();
  this.accessCount += 1;
  return this.save();
};

// 实例方法：检查用户在区服是否已有角色
UserCharacterMappingSchema.statics.hasCharacterInServer = function(userId: string, serverId: string) {
  return this.findOne({
    userId,
    serverId,
  });
};

// 扩展UserCharacterMappingDocument接口以包含自定义方法
export interface UserCharacterMappingDocument extends Omit<UserCharacterMapping, 'id'>, Document {
  updateLastAccessed(): Promise<UserCharacterMappingDocument>;
}
