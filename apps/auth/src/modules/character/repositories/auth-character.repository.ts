import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, FindOptionsWhere } from 'typeorm';
import { AuthCharacter } from '../entities/auth-character.entity';

/**
 * 角色基础信息Repository
 * 
 * 提供角色数据的CRUD操作和业务查询方法
 */
@Injectable()
export class AuthCharacterRepository {
  constructor(
    @InjectRepository(AuthCharacter)
    private readonly repository: Repository<AuthCharacter>,
  ) {}

  /**
   * 创建角色
   */
  async create(characterData: Partial<AuthCharacter>): Promise<AuthCharacter> {
    const character = this.repository.create(characterData);
    return await this.repository.save(character);
  }

  /**
   * 根据条件查找单个角色
   */
  async findOne(where: FindOptionsWhere<AuthCharacter>): Promise<AuthCharacter | null> {
    return await this.repository.findOne({ where });
  }

  /**
   * 根据条件查找多个角色
   */
  async find(where: FindOptionsWhere<AuthCharacter>): Promise<AuthCharacter[]> {
    return await this.repository.find({ where });
  }

  /**
   * 根据角色ID查找
   */
  async findByCharacterId(characterId: string): Promise<AuthCharacter | null> {
    return await this.repository.findOne({
      where: { characterId },
    });
  }

  /**
   * 根据用户ID查找所有角色
   */
  async findByUserId(userId: string): Promise<AuthCharacter[]> {
    return await this.repository.find({
      where: { userId, status: 'active' },
      order: { createdAt: 'DESC' },
    });
  }

  /**
   * 根据用户ID和区服ID查找角色
   */
  async findByUserAndServer(userId: string, serverId: string): Promise<AuthCharacter | null> {
    return await this.repository.findOne({
      where: { userId, serverId, status: 'active' },
    });
  }

  /**
   * 检查角色名称在区服内是否存在
   */
  async isCharacterNameExists(serverId: string, characterName: string): Promise<boolean> {
    const count = await this.repository.count({
      where: {
        serverId,
        characterName,
        status: 'active',
      },
    });
    return count > 0;
  }

  /**
   * 更新角色信息
   */
  async update(characterId: string, updateData: Partial<AuthCharacter>): Promise<AuthCharacter> {
    await this.repository.update({ characterId }, updateData);
    const updated = await this.findByCharacterId(characterId);
    if (!updated) {
      throw new Error(`Character ${characterId} not found after update`);
    }
    return updated;
  }

  /**
   * 更新角色登录信息
   */
  async updateLoginInfo(characterId: string): Promise<void> {
    await this.repository.update(
      { characterId },
      {
        lastLoginAt: new Date(),
        loginCount: () => 'login_count + 1',
      },
    );
  }

  /**
   * 软删除角色
   */
  async softDelete(characterId: string): Promise<void> {
    await this.repository.update(
      { characterId },
      {
        status: 'deleted',
        deletedAt: new Date(),
      },
    );
  }

  /**
   * 物理删除角色
   */
  async delete(characterId: string): Promise<void> {
    await this.repository.delete({ characterId });
  }

  /**
   * 统计用户在指定区服的角色数量
   */
  async countUserCharactersInServer(userId: string, serverId: string): Promise<number> {
    return await this.repository.count({
      where: { userId, serverId, status: 'active' },
    });
  }

  /**
   * 获取区服内的角色数量
   */
  async countCharactersInServer(serverId: string): Promise<number> {
    return await this.repository.count({
      where: { serverId, status: 'active' },
    });
  }

  /**
   * 批量更新角色状态
   */
  async batchUpdateStatus(
    characterIds: string[],
    status: AuthCharacter['status'],
    reason?: string,
  ): Promise<void> {
    const updateData: Partial<AuthCharacter> = { status };
    if (status === 'banned' && reason) {
      updateData.banReason = reason;
      updateData.banExpiresAt = null; // 永久封禁
    }

    await this.repository.update(
      { characterId: { $in: characterIds } as any },
      updateData,
    );
  }
}
