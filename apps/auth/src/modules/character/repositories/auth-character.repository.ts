import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, FilterQuery } from 'mongoose';
import { AuthCharacter, AuthCharacterDocument } from '../entities/auth-character.entity';

/**
 * 角色基础信息Repository
 *
 * 提供角色数据的CRUD操作和业务查询方法
 */
@Injectable()
export class AuthCharacterRepository {
  constructor(
    @InjectModel(AuthCharacter.name)
    private readonly characterModel: Model<AuthCharacterDocument>,
  ) {}

  /**
   * 创建角色
   */
  async create(characterData: Partial<AuthCharacter>): Promise<AuthCharacterDocument> {
    const character = new this.characterModel(characterData);
    return await character.save();
  }

  /**
   * 根据条件查找单个角色
   */
  async findOne(filter: FilterQuery<AuthCharacterDocument>): Promise<AuthCharacterDocument | null> {
    return await this.characterModel.findOne(filter).exec();
  }

  /**
   * 根据条件查找多个角色
   */
  async find(filter: FilterQuery<AuthCharacterDocument>): Promise<AuthCharacterDocument[]> {
    return await this.characterModel.find(filter).exec();
  }

  /**
   * 根据角色ID查找
   */
  async findByCharacterId(characterId: string): Promise<AuthCharacterDocument | null> {
    return await this.characterModel.findOne({ characterId }).exec();
  }

  /**
   * 根据用户ID查找所有角色
   */
  async findByUserId(userId: string): Promise<AuthCharacterDocument[]> {
    return await this.characterModel
      .find({ userId, status: 'active' })
      .sort({ createdAt: -1 })
      .exec();
  }

  /**
   * 根据用户ID和区服ID查找角色
   */
  async findByUserAndServer(userId: string, serverId: string): Promise<AuthCharacterDocument | null> {
    return await this.characterModel
      .findOne({ userId, serverId, status: 'active' })
      .exec();
  }

  /**
   * 检查角色名称在区服内是否存在
   */
  async isCharacterNameExists(serverId: string, characterName: string): Promise<boolean> {
    const character = await this.characterModel
      .findOne({
        serverId,
        characterName,
        status: 'active',
      })
      .exec();
    return !!character;
  }

  /**
   * 更新角色信息
   */
  async update(characterId: string, updateData: Partial<AuthCharacter>): Promise<AuthCharacterDocument> {
    const updated = await this.characterModel
      .findOneAndUpdate(
        { characterId },
        { $set: updateData },
        { new: true }
      )
      .exec();

    if (!updated) {
      throw new Error(`Character ${characterId} not found after update`);
    }
    return updated;
  }

  /**
   * 更新角色登录信息
   */
  async updateLoginInfo(characterId: string): Promise<void> {
    await this.characterModel
      .updateOne(
        { characterId },
        {
          $set: { lastLoginAt: new Date() },
          $inc: { loginCount: 1 },
        }
      )
      .exec();
  }

  /**
   * 软删除角色
   */
  async softDelete(characterId: string): Promise<void> {
    await this.characterModel
      .updateOne(
        { characterId },
        {
          $set: {
            status: 'deleted',
            deletedAt: new Date(),
          },
        }
      )
      .exec();
  }

  /**
   * 物理删除角色
   */
  async delete(characterId: string): Promise<void> {
    await this.characterModel.deleteOne({ characterId }).exec();
  }

  /**
   * 统计用户在指定区服的角色数量
   */
  async countUserCharactersInServer(userId: string, serverId: string): Promise<number> {
    return await this.characterModel
      .countDocuments({ userId, serverId, status: 'active' })
      .exec();
  }

  /**
   * 获取区服内的角色数量
   */
  async countCharactersInServer(serverId: string): Promise<number> {
    return await this.characterModel
      .countDocuments({ serverId, status: 'active' })
      .exec();
  }

  /**
   * 批量更新角色状态
   */
  async batchUpdateStatus(
    characterIds: string[],
    status: AuthCharacter['status'],
    reason?: string,
  ): Promise<void> {
    const updateData: any = { status };
    if (status === 'banned' && reason) {
      updateData.banReason = reason;
      updateData.banExpiresAt = null; // 永久封禁
    }

    await this.characterModel
      .updateMany(
        { characterId: { $in: characterIds } },
        { $set: updateData }
      )
      .exec();
  }
}
