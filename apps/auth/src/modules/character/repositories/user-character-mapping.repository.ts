import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, FindOptionsWhere } from 'typeorm';
import { UserCharacterMapping } from '../entities/user-character-mapping.entity';

/**
 * 用户角色映射Repository
 * 
 * 提供用户角色关系的CRUD操作和查询方法
 */
@Injectable()
export class UserCharacterMappingRepository {
  constructor(
    @InjectRepository(UserCharacterMapping)
    private readonly repository: Repository<UserCharacterMapping>,
  ) {}

  /**
   * 创建用户角色映射
   */
  async create(mappingData: Partial<UserCharacterMapping>): Promise<UserCharacterMapping> {
    const mapping = this.repository.create(mappingData);
    return await this.repository.save(mapping);
  }

  /**
   * 根据条件查找单个映射
   */
  async findOne(where: FindOptionsWhere<UserCharacterMapping>): Promise<UserCharacterMapping | null> {
    return await this.repository.findOne({ where });
  }

  /**
   * 根据条件查找多个映射
   */
  async find(where: FindOptionsWhere<UserCharacterMapping>): Promise<UserCharacterMapping[]> {
    return await this.repository.find({ where });
  }

  /**
   * 根据用户ID和区服ID查找映射
   */
  async findByUserAndServer(userId: string, serverId: string): Promise<UserCharacterMapping | null> {
    return await this.repository.findOne({
      where: { userId, serverId },
      relations: ['character'],
    });
  }

  /**
   * 根据角色ID查找映射
   */
  async findByCharacterId(characterId: string): Promise<UserCharacterMapping | null> {
    return await this.repository.findOne({
      where: { characterId },
      relations: ['character'],
    });
  }

  /**
   * 根据用户ID查找所有映射
   */
  async findByUserId(userId: string): Promise<UserCharacterMapping[]> {
    return await this.repository.find({
      where: { userId },
      relations: ['character'],
      order: { createdAt: 'DESC' },
    });
  }

  /**
   * 更新映射信息
   */
  async update(id: string, updateData: Partial<UserCharacterMapping>): Promise<UserCharacterMapping> {
    await this.repository.update({ id }, updateData);
    const updated = await this.repository.findOne({ where: { id } });
    if (!updated) {
      throw new Error(`Mapping ${id} not found after update`);
    }
    return updated;
  }

  /**
   * 更新最后访问时间
   */
  async updateLastAccessed(characterId: string): Promise<void> {
    await this.repository.update(
      { characterId },
      {
        lastAccessedAt: new Date(),
        accessCount: () => 'access_count + 1',
      },
    );
  }

  /**
   * 删除映射
   */
  async delete(id: string): Promise<void> {
    await this.repository.delete({ id });
  }

  /**
   * 根据角色ID删除映射
   */
  async deleteByCharacterId(characterId: string): Promise<void> {
    await this.repository.delete({ characterId });
  }

  /**
   * 根据用户ID删除所有映射
   */
  async deleteByUserId(userId: string): Promise<void> {
    await this.repository.delete({ userId });
  }

  /**
   * 检查用户在区服是否已有角色
   */
  async hasCharacterInServer(userId: string, serverId: string): Promise<boolean> {
    const count = await this.repository.count({
      where: { userId, serverId },
    });
    return count > 0;
  }

  /**
   * 统计用户的角色数量
   */
  async countUserCharacters(userId: string): Promise<number> {
    return await this.repository.count({
      where: { userId },
    });
  }

  /**
   * 获取区服内的用户数量
   */
  async countUsersInServer(serverId: string): Promise<number> {
    return await this.repository.count({
      where: { serverId },
    });
  }

  /**
   * 批量更新权限
   */
  async batchUpdatePermissions(
    characterIds: string[],
    permissions: UserCharacterMapping['permissions'],
  ): Promise<void> {
    await this.repository.update(
      { characterId: { $in: characterIds } as any },
      { permissions },
    );
  }

  /**
   * 获取具有特定权限的角色映射
   */
  async findByPermission(permission: string, value: boolean = true): Promise<UserCharacterMapping[]> {
    return await this.repository
      .createQueryBuilder('mapping')
      .leftJoinAndSelect('mapping.character', 'character')
      .where(`JSON_EXTRACT(mapping.permissions, '$.${permission}') = :value`, { value })
      .getMany();
  }
}
