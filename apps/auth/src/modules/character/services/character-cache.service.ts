import { Injectable, Logger } from '@nestjs/common';
import { RedisService } from '@common/redis';
import { CharacterResponseDto } from '../dto/create-character.dto';

/**
 * 角色缓存服务
 * 
 * 提供角色信息的缓存管理
 */
@Injectable()
export class CharacterCacheService {
  private readonly logger = new Logger(CharacterCacheService.name);

  // 缓存键设计
  private readonly CACHE_KEYS = {
    // 用户在指定区服的角色
    USER_CHARACTER: (userId: string, serverId: string) => 
      `auth:user_character:${userId}:${serverId}`,
    
    // 角色基础信息
    CHARACTER_INFO: (characterId: string) => 
      `auth:character:${characterId}`,
    
    // 用户的所有角色
    USER_ALL_CHARACTERS: (userId: string) => 
      `auth:user_characters:${userId}`,
    
    // 区服角色名称索引
    SERVER_CHARACTER_NAMES: (serverId: string) => 
      `auth:server_names:${serverId}`,
  };

  // 缓存TTL配置
  private readonly CACHE_TTL = {
    CHARACTER_INFO: 3600,      // 1小时
    USER_CHARACTERS: 1800,     // 30分钟
    CHARACTER_NAMES: 7200,     // 2小时
  };

  constructor(
    private readonly redisService: RedisService,
  ) {}

  /**
   * 设置角色基础信息缓存
   */
  async setCharacterInfo(characterId: string, character: CharacterResponseDto): Promise<void> {
    try {
      const key = this.CACHE_KEYS.CHARACTER_INFO(characterId);
      await this.redisService.setex(key, this.CACHE_TTL.CHARACTER_INFO, JSON.stringify(character));
      this.logger.debug(`✅ 设置角色缓存成功: ${key}`);
    } catch (error) {
      this.logger.error(`❌ 设置角色缓存失败: ${characterId}`, error);
      // 缓存失败不影响业务逻辑，只记录错误
    }
  }

  /**
   * 获取角色基础信息缓存
   */
  async getCharacterInfo(characterId: string): Promise<CharacterResponseDto | null> {
    try {
      const key = this.CACHE_KEYS.CHARACTER_INFO(characterId);
      const cached = await this.redisService.get(key);
      if (cached) {
        this.logger.debug(`✅ 命中角色缓存: ${key}`);
        return JSON.parse(cached);
      }
      this.logger.debug(`❌ 角色缓存未命中: ${key}`);
      return null;
    } catch (error) {
      this.logger.error(`❌ 获取角色缓存失败: ${characterId}`, error);
      return null;
    }
  }

  /**
   * 设置用户在指定区服的角色缓存
   */
  async setUserCharacterInServer(
    userId: string,
    serverId: string,
    character: CharacterResponseDto,
  ): Promise<void> {
    try {
      const key = this.CACHE_KEYS.USER_CHARACTER(userId, serverId);
      await this.redisService.setex(key, this.CACHE_TTL.USER_CHARACTERS, JSON.stringify(character));
      this.logger.debug(`✅ 设置用户区服角色缓存成功: ${key}`);
    } catch (error) {
      this.logger.error(`❌ 设置用户区服角色缓存失败: ${userId}:${serverId}`, error);
    }
  }

  /**
   * 获取用户在指定区服的角色缓存
   */
  async getUserCharacterInServer(userId: string, serverId: string): Promise<CharacterResponseDto | null> {
    try {
      const key = this.CACHE_KEYS.USER_CHARACTER(userId, serverId);
      const cached = await this.redisService.get(key);
      if (cached) {
        this.logger.debug(`✅ 命中用户区服角色缓存: ${key}`);
        return JSON.parse(cached);
      }
      this.logger.debug(`❌ 用户区服角色缓存未命中: ${key}`);
      return null;
    } catch (error) {
      this.logger.error(`❌ 获取用户区服角色缓存失败: ${userId}:${serverId}`, error);
      return null;
    }
  }

  /**
   * 设置用户所有角色缓存
   */
  async setUserAllCharacters(userId: string, characters: CharacterResponseDto[]): Promise<void> {
    try {
      const key = this.CACHE_KEYS.USER_ALL_CHARACTERS(userId);
      await this.redisService.setex(key, this.CACHE_TTL.USER_CHARACTERS, JSON.stringify(characters));
      this.logger.debug(`✅ 设置用户所有角色缓存成功: ${key}, 角色数量: ${characters.length}`);
    } catch (error) {
      this.logger.error(`❌ 设置用户所有角色缓存失败: ${userId}`, error);
    }
  }

  /**
   * 获取用户所有角色缓存
   */
  async getUserAllCharacters(userId: string): Promise<CharacterResponseDto[] | null> {
    try {
      const key = this.CACHE_KEYS.USER_ALL_CHARACTERS(userId);
      const cached = await this.redisService.get(key);
      if (cached) {
        const characters = JSON.parse(cached);
        this.logger.debug(`✅ 命中用户所有角色缓存: ${key}, 角色数量: ${characters.length}`);
        return characters;
      }
      this.logger.debug(`❌ 用户所有角色缓存未命中: ${key}`);
      return null;
    } catch (error) {
      this.logger.error(`❌ 获取用户所有角色缓存失败: ${userId}`, error);
      return null;
    }
  }

  /**
   * 清除角色相关缓存
   */
  async clearCharacterCache(characterId: string, userId?: string, serverId?: string): Promise<void> {
    try {
      const keysToDelete = [
        this.CACHE_KEYS.CHARACTER_INFO(characterId),
      ];

      if (userId) {
        keysToDelete.push(this.CACHE_KEYS.USER_ALL_CHARACTERS(userId));
        
        if (serverId) {
          keysToDelete.push(this.CACHE_KEYS.USER_CHARACTER(userId, serverId));
        }
      }

      // TODO: 实现Redis缓存删除
      // await this.redisService.del(...keysToDelete);
      this.logger.debug(`清除角色缓存: ${keysToDelete.join(', ')}`);
    } catch (error) {
      this.logger.error(`清除角色缓存失败: ${characterId}`, error);
    }
  }

  /**
   * 清除用户所有角色缓存
   */
  async clearUserCharacterCache(userId: string): Promise<void> {
    try {
      // TODO: 实现模糊匹配删除
      // const pattern = `auth:user_character:${userId}:*`;
      // const keys = await this.redisService.keys(pattern);
      // if (keys.length > 0) {
      //   await this.redisService.del(...keys);
      // }
      
      // 删除用户所有角色缓存
      const userAllCharactersKey = this.CACHE_KEYS.USER_ALL_CHARACTERS(userId);
      // await this.redisService.del(userAllCharactersKey);
      
      this.logger.debug(`清除用户所有角色缓存: ${userId}`);
    } catch (error) {
      this.logger.error(`清除用户角色缓存失败: ${userId}`, error);
    }
  }

  /**
   * 预热缓存
   */
  async warmupCache(userId: string): Promise<void> {
    try {
      // TODO: 实现缓存预热逻辑
      // 1. 预加载用户的所有角色
      // 2. 预加载每个角色的详细信息
      this.logger.debug(`预热用户缓存: ${userId}`);
    } catch (error) {
      this.logger.error(`预热缓存失败: ${userId}`, error);
    }
  }

  /**
   * 清除角色相关的所有缓存
   */
  async clearCharacterCache(characterId: string, userId?: string): Promise<void> {
    try {
      const keysToDelete = [
        this.CACHE_KEYS.CHARACTER_INFO(characterId),
      ];

      if (userId) {
        // 清除用户相关缓存
        keysToDelete.push(this.CACHE_KEYS.USER_ALL_CHARACTERS(userId));

        // 清除所有区服的用户角色缓存（这里简化处理，实际可能需要遍历所有区服）
        // 在实际应用中，可以维护一个用户-区服映射来精确清理
      }

      await Promise.all(keysToDelete.map(key => this.redisService.del(key)));
      this.logger.debug(`✅ 清除角色缓存成功: ${characterId}, 清除键数量: ${keysToDelete.length}`);
    } catch (error) {
      this.logger.error(`❌ 清除角色缓存失败: ${characterId}`, error);
    }
  }

  /**
   * 清除用户所有角色缓存
   */
  async clearUserCache(userId: string): Promise<void> {
    try {
      const pattern = `auth:character:user:${userId}:*`;
      const keys = await this.redisService.keys(pattern);

      if (keys.length > 0) {
        await this.redisService.del(...keys);
        this.logger.debug(`✅ 清除用户缓存成功: ${userId}, 清除键数量: ${keys.length}`);
      } else {
        this.logger.debug(`用户缓存为空: ${userId}`);
      }
    } catch (error) {
      this.logger.error(`❌ 清除用户缓存失败: ${userId}`, error);
    }
  }

  /**
   * 批量预热缓存
   */
  async warmupCache(userId: string, characters: CharacterResponseDto[]): Promise<void> {
    try {
      // 设置用户所有角色缓存
      await this.setUserAllCharacters(userId, characters);

      // 设置每个角色的详细信息缓存
      await Promise.all(
        characters.map(character =>
          this.setCharacterInfo(character.characterId, character)
        )
      );

      this.logger.debug(`✅ 缓存预热成功: ${userId}, 角色数量: ${characters.length}`);
    } catch (error) {
      this.logger.error(`❌ 缓存预热失败: ${userId}`, error);
    }
  }
}
