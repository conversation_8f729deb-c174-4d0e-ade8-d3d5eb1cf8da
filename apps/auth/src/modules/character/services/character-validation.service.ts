import { Injectable, Logger } from '@nestjs/common';
import { AuthCharacterRepository } from '../repositories/auth-character.repository';
import { UserCharacterMappingRepository } from '../repositories/user-character-mapping.repository';

/**
 * 角色验证服务
 * 
 * 提供角色相关的验证逻辑
 */
@Injectable()
export class CharacterValidationService {
  private readonly logger = new Logger(CharacterValidationService.name);

  constructor(
    private readonly authCharacterRepository: AuthCharacterRepository,
    private readonly userCharacterMappingRepository: UserCharacterMappingRepository,
  ) {}

  /**
   * 验证用户存在（占位方法，实际应该注入UserService）
   */
  async validateUserExists(userId: string): Promise<boolean> {
    // TODO: 注入UserService并验证用户存在
    // const user = await this.userService.findById(userId);
    // return !!user;
    
    // 临时实现：假设用户存在
    this.logger.debug(`验证用户存在: ${userId}`);
    return true;
  }

  /**
   * 验证角色名称在区服内是否唯一
   */
  async isCharacterNameExists(serverId: string, characterName: string): Promise<boolean> {
    return await this.authCharacterRepository.isCharacterNameExists(serverId, characterName);
  }

  /**
   * 验证用户在区服是否已有角色
   */
  async hasCharacterInServer(userId: string, serverId: string): Promise<boolean> {
    const character = await this.authCharacterRepository.findByUserAndServer(userId, serverId);
    return !!character;
  }

  /**
   * 验证角色名称格式
   */
  validateCharacterNameFormat(characterName: string): { valid: boolean; message?: string } {
    // 基础长度检查
    if (characterName.length < 2 || characterName.length > 50) {
      return {
        valid: false,
        message: '角色名称长度必须在2-50个字符之间',
      };
    }

    // 检查是否包含非法字符
    const illegalChars = /[<>'"&\\\/\s]/;
    if (illegalChars.test(characterName)) {
      return {
        valid: false,
        message: '角色名称不能包含特殊字符：< > \' " & \\ / 空格',
      };
    }

    // 检查是否为纯数字
    if (/^\d+$/.test(characterName)) {
      return {
        valid: false,
        message: '角色名称不能为纯数字',
      };
    }

    // 检查敏感词（简单示例）
    const sensitiveWords = ['admin', 'system', 'test', 'gm', '管理员', '系统'];
    const lowerName = characterName.toLowerCase();
    for (const word of sensitiveWords) {
      if (lowerName.includes(word)) {
        return {
          valid: false,
          message: '角色名称包含敏感词汇',
        };
      }
    }

    return { valid: true };
  }

  /**
   * 验证区服ID格式
   */
  validateServerIdFormat(serverId: string): { valid: boolean; message?: string } {
    // 基础格式检查
    if (!/^server\d{3}$/.test(serverId)) {
      return {
        valid: false,
        message: '区服ID格式错误，应为server001格式',
      };
    }

    return { valid: true };
  }

  /**
   * 验证角色是否可以被删除
   */
  async canDeleteCharacter(characterId: string): Promise<{ canDelete: boolean; reason?: string }> {
    const character = await this.authCharacterRepository.findByCharacterId(characterId);
    
    if (!character) {
      return {
        canDelete: false,
        reason: '角色不存在',
      };
    }

    if (character.status === 'deleted') {
      return {
        canDelete: false,
        reason: '角色已被删除',
      };
    }

    // 检查角色是否有重要数据（这里可以扩展更多业务逻辑）
    if (character.loginCount > 100) {
      return {
        canDelete: false,
        reason: '角色登录次数过多，不建议删除',
      };
    }

    return { canDelete: true };
  }

  /**
   * 验证角色状态转换是否合法
   */
  validateStatusTransition(
    currentStatus: string,
    newStatus: string,
  ): { valid: boolean; message?: string } {
    const validTransitions: Record<string, string[]> = {
      active: ['inactive', 'banned', 'deleted'],
      inactive: ['active', 'banned', 'deleted'],
      banned: ['active', 'inactive', 'deleted'],
      deleted: [], // 删除状态不可逆
    };

    if (!validTransitions[currentStatus]?.includes(newStatus)) {
      return {
        valid: false,
        message: `不能从状态 ${currentStatus} 转换到 ${newStatus}`,
      };
    }

    return { valid: true };
  }

  /**
   * 验证角色权限
   */
  validateCharacterPermissions(permissions: any): { valid: boolean; message?: string } {
    if (!permissions || typeof permissions !== 'object') {
      return { valid: true }; // 权限为空是合法的
    }

    const allowedPermissions = [
      'canTrade',
      'canChat',
      'canCreateGuild',
      'canParticipateMatch',
    ];

    for (const key of Object.keys(permissions)) {
      if (!allowedPermissions.includes(key)) {
        return {
          valid: false,
          message: `未知的权限字段: ${key}`,
        };
      }

      if (typeof permissions[key] !== 'boolean') {
        return {
          valid: false,
          message: `权限字段 ${key} 必须为布尔值`,
        };
      }
    }

    return { valid: true };
  }
}
