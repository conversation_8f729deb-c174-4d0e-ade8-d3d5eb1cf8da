import { Injectable, Logger, ConflictException, NotFoundException, UnauthorizedException } from '@nestjs/common';
import { AuthCharacterRepository } from '../repositories/auth-character.repository';
import { UserCharacterMappingRepository } from '../repositories/user-character-mapping.repository';
import { CharacterValidationService } from './character-validation.service';
import { CharacterCacheService } from './character-cache.service';
import { CreateCharacterDto, UpdateCharacterDto, CharacterResponseDto, CharacterDetailResponseDto } from '../dto/create-character.dto';
import { AuthCharacter, AuthCharacterDocument } from '../entities/auth-character.entity';
import { UserCharacterMapping, UserCharacterMappingDocument } from '../entities/user-character-mapping.entity';
import * as crypto from 'crypto';

/**
 * 角色管理服务
 * 
 * 职责：
 * - 角色ID生成和管理
 * - 角色基础信息CRUD
 * - 角色名称唯一性验证
 * - 用户角色关系管理
 */
@Injectable()
export class CharacterService {
  private readonly logger = new Logger(CharacterService.name);

  constructor(
    private readonly authCharacterRepository: AuthCharacterRepository,
    private readonly userCharacterMappingRepository: UserCharacterMappingRepository,
    private readonly characterCacheService: CharacterCacheService,
    private readonly characterValidationService: CharacterValidationService,
    // TODO: 注入微服务客户端
    // private readonly microserviceClient: MicroserviceClientService,
  ) {}

  /**
   * 创建角色（HTTP接口调用）
   */
  async createCharacter(
    userId: string,
    createDto: CreateCharacterDto,
  ): Promise<CharacterResponseDto> {
    this.logger.log(`🎮 开始创建角色: userId=${userId}, serverId=${createDto.serverId}`);
    
    try {
      // 1. 验证用户权限（由HTTP Guard已验证，这里做二次确认）
      await this.characterValidationService.validateUserExists(userId);
      
      // 2. 验证区服ID格式
      const serverValidation = this.characterValidationService.validateServerIdFormat(createDto.serverId);
      if (!serverValidation.valid) {
        throw new ConflictException(serverValidation.message);
      }
      
      // 3. 验证角色名称格式
      const nameFormatValidation = this.characterValidationService.validateCharacterNameFormat(createDto.characterName);
      if (!nameFormatValidation.valid) {
        throw new ConflictException(nameFormatValidation.message);
      }
      
      // 4. 检查区服角色限制（核心业务规则）
      const existingCharacter = await this.getUserCharacterInServer(userId, createDto.serverId);
      if (existingCharacter) {
        throw new ConflictException(`用户在区服${createDto.serverId}已存在角色`);
      }
      
      // 5. 验证角色名称唯一性
      const nameExists = await this.characterValidationService.isCharacterNameExists(
        createDto.serverId, 
        createDto.characterName
      );
      if (nameExists) {
        throw new ConflictException(`角色名称"${createDto.characterName}"已存在`);
      }
      
      // 6. 生成安全的角色ID（Auth服务权威）
      const characterId = this.generateSecureCharacterId(userId, createDto.serverId);
      
      // 7. 创建角色记录（事务处理）
      const character = await this.createCharacterWithTransaction({
        characterId,
        userId,
        serverId: createDto.serverId,
        characterName: createDto.characterName,
        displayName: createDto.displayName,
        metadata: {
          faceIcon: createDto.faceIcon,
          avatar: createDto.avatar,
          level: 1,
          vipLevel: 0,
          createdFrom: 'http_api',
        },
      });
      
      // 8. 异步通知Character服务初始化游戏数据
      setImmediate(async () => {
        try {
          await this.notifyCharacterServiceForInitialization(character);
        } catch (error) {
          this.logger.error(`通知Character服务失败: ${characterId}`, error);
          // 这里可以实现补偿机制或重试队列
        }
      });
      
      // 9. 更新缓存
      const response = this.mapToCharacterResponse(character);
      await this.characterCacheService.setCharacterInfo(characterId, response);
      await this.characterCacheService.setUserCharacterInServer(userId, createDto.serverId, response);
      
      this.logger.log(`✅ 角色创建成功: ${characterId} (${createDto.characterName})`);
      
      return response;
      
    } catch (error) {
      this.logger.error(`❌ 角色创建失败: userId=${userId}, serverId=${createDto.serverId}`, error);
      throw error;
    }
  }

  /**
   * 获取用户在指定区服的角色
   */
  async getUserCharacterInServer(userId: string, serverId: string): Promise<CharacterResponseDto | null> {
    // 1. 先查缓存
    const cached = await this.characterCacheService.getUserCharacterInServer(userId, serverId);
    if (cached) {
      return cached;
    }
    
    // 2. 查数据库
    const character = await this.authCharacterRepository.findByUserAndServer(userId, serverId);
    
    if (!character) {
      return null;
    }
    
    const response = this.mapToCharacterResponse(character);
    
    // 3. 更新缓存
    await this.characterCacheService.setUserCharacterInServer(userId, serverId, response);
    
    return response;
  }

  /**
   * 获取用户的所有角色
   */
  async getUserAllCharacters(userId: string): Promise<CharacterResponseDto[]> {
    // 1. 先查缓存
    const cached = await this.characterCacheService.getUserAllCharacters(userId);
    if (cached) {
      return cached;
    }
    
    // 2. 查数据库
    const characters = await this.authCharacterRepository.findByUserId(userId);
    
    const responses = characters.map(char => this.mapToCharacterResponse(char));
    
    // 3. 更新缓存
    await this.characterCacheService.setUserAllCharacters(userId, responses);
    
    return responses;
  }

  /**
   * 获取角色详细信息
   */
  async getCharacterDetail(characterId: string): Promise<CharacterDetailResponseDto> {
    const character = await this.authCharacterRepository.findByCharacterId(characterId);
    
    if (!character) {
      throw new NotFoundException(`角色 ${characterId} 不存在`);
    }
    
    return this.mapToCharacterDetailResponse(character);
  }

  /**
   * 验证角色归属（权威验证）
   */
  async validateCharacterOwnership(
    userId: string, 
    characterId: string, 
    serverId?: string
  ): Promise<boolean> {
    const character = await this.authCharacterRepository.findOne({
      characterId,
      userId,
      status: 'active',
      ...(serverId && { serverId }),
    });
    
    if (!character) {
      throw new UnauthorizedException('角色不存在或不属于当前用户');
    }
    
    return true;
  }

  /**
   * 更新角色基础信息
   */
  async updateCharacter(characterId: string, updateDto: UpdateCharacterDto): Promise<CharacterResponseDto> {
    const character = await this.authCharacterRepository.findByCharacterId(characterId);
    
    if (!character) {
      throw new NotFoundException(`角色 ${characterId} 不存在`);
    }
    
    // 更新metadata
    const updatedMetadata = {
      ...character.metadata,
      ...(updateDto.faceIcon && { faceIcon: updateDto.faceIcon }),
      ...(updateDto.avatar && { avatar: updateDto.avatar }),
    };
    
    const updatedCharacter = await this.authCharacterRepository.update(characterId, {
      displayName: updateDto.displayName,
      metadata: updatedMetadata,
    });
    
    const response = this.mapToCharacterResponse(updatedCharacter);
    
    // 清除相关缓存
    await this.characterCacheService.clearCharacterCache(characterId, character.userId, character.serverId);
    
    return response;
  }

  /**
   * 更新角色登录信息
   */
  async updateLoginInfo(characterId: string): Promise<void> {
    await this.authCharacterRepository.updateLoginInfo(characterId);
    
    // 清除缓存
    const character = await this.authCharacterRepository.findByCharacterId(characterId);
    if (character) {
      await this.characterCacheService.clearCharacterCache(characterId, character.userId, character.serverId);
    }
  }

  /**
   * 软删除角色
   */
  async softDeleteCharacter(characterId: string): Promise<void> {
    const character = await this.authCharacterRepository.findByCharacterId(characterId);
    
    if (!character) {
      throw new NotFoundException(`角色 ${characterId} 不存在`);
    }
    
    // 检查是否可以删除
    const canDelete = await this.characterValidationService.canDeleteCharacter(characterId);
    if (!canDelete.canDelete) {
      throw new ConflictException(canDelete.reason);
    }
    
    await this.authCharacterRepository.softDelete(characterId);
    
    // 清除缓存
    await this.characterCacheService.clearCharacterCache(characterId, character.userId, character.serverId);
    
    this.logger.log(`角色软删除成功: ${characterId}`);
  }

  /**
   * 生成安全的角色ID
   */
  private generateSecureCharacterId(userId: string, serverId: string): string {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substr(2, 9);
    const hash = crypto.createHash('md5')
      .update(`${userId}:${serverId}:${timestamp}`)
      .digest('hex')
      .substr(0, 8);

    return `char_${serverId}_${hash}_${random}`;
  }

  /**
   * 创建角色记录（事务处理）
   */
  private async createCharacterWithTransaction(characterData: Partial<AuthCharacter>): Promise<AuthCharacter> {
    // TODO: 实现事务处理
    // 这里应该使用数据库事务来确保数据一致性

    try {
      // 1. 创建角色记录
      const character = await this.authCharacterRepository.create({
        ...characterData,
        status: 'active',
        createdAt: new Date(),
        lastLoginAt: new Date(),
        loginCount: 0,
        totalPlayTime: 0,
      });

      // 2. 创建用户角色映射
      await this.userCharacterMappingRepository.create({
        userId: characterData.userId!,
        serverId: characterData.serverId!,
        characterId: characterData.characterId!,
        isPrimary: true,
        roleType: 'main',
        createdAt: new Date(),
        lastAccessedAt: new Date(),
        accessCount: 0,
      });

      return character;
    } catch (error) {
      this.logger.error('创建角色事务失败', error);
      throw error;
    }
  }

  /**
   * 通知Character服务初始化角色数据
   */
  private async notifyCharacterServiceForInitialization(character: AuthCharacter): Promise<void> {
    try {
      // TODO: 实现微服务调用
      // await this.microserviceClient.call('character', 'character.initializeFromAuth', {
      //   characterId: character.characterId,
      //   userId: character.userId,
      //   serverId: character.serverId,
      //   characterName: character.characterName,
      //   initialData: character.metadata,
      // });

      this.logger.log(`通知Character服务初始化: ${character.characterId}`);
    } catch (error) {
      this.logger.error(`通知Character服务失败: ${character.characterId}`, error);
      throw error;
    }
  }

  /**
   * 映射到角色响应DTO
   */
  private mapToCharacterResponse(character: AuthCharacter): CharacterResponseDto {
    return {
      characterId: character.characterId,
      userId: character.userId,
      serverId: character.serverId,
      characterName: character.characterName,
      displayName: character.displayName,
      status: character.status,
      createdAt: character.createdAt,
      lastLoginAt: character.lastLoginAt,
      loginCount: character.loginCount,
      metadata: character.metadata,
    };
  }

  /**
   * 映射到角色详细响应DTO
   */
  private mapToCharacterDetailResponse(character: AuthCharacter): CharacterDetailResponseDto {
    return {
      ...this.mapToCharacterResponse(character),
      totalPlayTime: character.totalPlayTime,
      banReason: character.banReason,
      banExpiresAt: character.banExpiresAt,
    };
  }
}