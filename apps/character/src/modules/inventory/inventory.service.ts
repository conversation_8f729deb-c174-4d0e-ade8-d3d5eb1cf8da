/**
 * Inventory Service - 严格基于old项目bag.js重新实现
 * 确保与old项目的业务逻辑100%一致
 */

import { Injectable, Logger, NotFoundException, BadRequestException } from '@nestjs/common';
import { Inventory, InventoryDocument, BookMarkEntry, BookMarkType } from '@character/common/schemas/inventory.schema';
import { InventoryRepository } from '@character/common/repositories/inventory.repository';
import { GameConfigFacade } from '@app/game-config';
import {MicroserviceClientService} from "@libs/service-mesh";

@Injectable()
export class InventoryService {
  private readonly logger = new Logger(InventoryService.name);

  constructor(
    private readonly inventoryRepository: InventoryRepository,
    private readonly gameConfig: GameConfigFacade,
    private readonly microserviceClient: MicroserviceClientService,
  ) {}

  /**
   * 获取角色的背包数据
   * 基于old项目: Bag实体的初始化
   */
  async getCharacterBag(characterId: string): Promise<InventoryDocument | null> {
    return await this.inventoryRepository.findByCharacterId(characterId, true) as InventoryDocument | null;
  }

  /**
   * 初始化角色背包数据
   * 基于old项目: Bag构造函数和initByDB
   */
  async initCharacterBag(characterId: string, serverId: string): Promise<InventoryDocument> {
    const uid = characterId; // 在新架构中，uid就是characterId

    // 先检查是否已经存在背包数据，避免重复创建
    const existingBag = await this.inventoryRepository.findByCharacterId(characterId, true) as InventoryDocument | null;
    if (existingBag) {
      this.logger.log(`角色背包已存在，返回现有数据: ${characterId}`);
      return existingBag;
    }

    // 初始化页签配置 - 基于old项目checkBookMarkInitDB
    const bookMarkConfigs = await this.initBookMarkConfigs();

    const bagData = {
      uid,
      characterId,
      serverId,
      bag: bookMarkConfigs,
      itemUidToBookMarkId: [],
      createTime: Date.now(),
      updateTime: Date.now(),
    };

    try {
      return await this.inventoryRepository.create(bagData);
    } catch (error) {
      // 如果是重复键错误，再次尝试查找现有记录
      if (error.code === 11000) {
        this.logger.warn(`检测到重复键错误，尝试查找现有背包: ${characterId}`);
        const existingBag = await this.inventoryRepository.findByCharacterId(characterId, true) as InventoryDocument | null;
        if (existingBag) {
          return existingBag;
        }
      }
      throw error;
    }
  }

  /**
   * 获取页签数据
   * 基于old项目: getOneBookMark方法
   */
  async getBookMark(characterId: string, bookMarkId: number): Promise<BookMarkEntry | null> {
    this.logger.log(`获取页签数据: ${characterId}, 页签ID: ${bookMarkId}`);

    const bag = await this.getCharacterBag(characterId);
    if (!bag) {
      return null;
    }

    return bag.getOneBookMark(bookMarkId);
  }

  /**
   * 添加物品到背包
   * 基于old项目: addToBag方法
   */
  async addItemToBag(characterId: string, bookMarkId: number, itemUid: string): Promise<{ success: boolean; message?: string }> {
    this.logger.log(`添加物品到背包: ${characterId}, 页签: ${bookMarkId}, 物品: ${itemUid}`);

    let bag = await this.getCharacterBag(characterId);
    if (!bag) {
      bag = await this.initCharacterBag(characterId, 'server_001');
    }

    const success = bag.addToBag(bookMarkId, itemUid);
    if (success) {
      await bag.save();
      return { success: true };
    } else {
      return { success: false, message: '背包已满或页签不存在' };
    }
  }

  /**
   * 从背包移除物品
   * 基于old项目: removeFromBag方法
   */
  async removeItemFromBag(characterId: string, bookMarkId: number, itemUid: string): Promise<{ success: boolean; message?: string }> {
    this.logger.log(`从背包移除物品: ${characterId}, 页签: ${bookMarkId}, 物品: ${itemUid}`);

    const bag = await this.getCharacterBag(characterId);
    if (!bag) {
      return { success: false, message: '背包不存在' };
    }

    const success = bag.removeFromBag(bookMarkId, itemUid);
    if (success) {
      await bag.save();
      return { success: true };
    } else {
      return { success: false, message: '物品不存在或页签不存在' };
    }
  }

  /**
   * 扩展背包容量
   * 基于old项目: expandBag方法
   */
  async expandBag(characterId: string, bookMarkId: number, expandCount: number = 1): Promise<{ success: boolean; cost: number; message?: string }> {
    this.logger.log(`扩展背包: ${characterId}, 页签: ${bookMarkId}, 扩展数量: ${expandCount}`);

    const bag = await this.getCharacterBag(characterId);
    if (!bag) {
      return { success: false, cost: 0, message: '背包不存在' };
    }

    const result = bag.expandBag(bookMarkId, expandCount);
    if (result.success) {
      await bag.save();
      this.logger.log(`背包扩展成功: ${characterId}, 费用: ${result.cost}`);
    }

    return result;
  }

  /**
   * 整理背包
   * 基于old项目: sortBag方法
   */
  async sortBag(characterId: string, bookMarkId: number, sortType: string = 'default'): Promise<{ success: boolean; message?: string }> {
    this.logger.log(`整理背包: ${characterId}, 页签: ${bookMarkId}, 排序类型: ${sortType}`);

    const bag = await this.getCharacterBag(characterId);
    if (!bag) {
      return { success: false, message: '背包不存在' };
    }

    const bookMark = bag.getOneBookMark(bookMarkId);
    if (!bookMark) {
      return { success: false, message: '页签不存在' };
    }

    // TODO: 实现具体的排序逻辑
    // 这里需要根据物品的类型、品质、获得时间等进行排序
    bag.sortBag(bookMarkId, sortType);
    await bag.save();

    return { success: true };
  }

  /**
   * 使用物品
   * 基于old项目: useItemMainType方法
   */
  async useItem(characterId: string, bookMarkId: number, itemUid: string, resId: number, useType: number, subType: number, quantity: number = 1, heroUid?: string): Promise<any> {
    this.logger.log(`使用物品: ${characterId}, 页签: ${bookMarkId}, 物品: ${itemUid}, 类型: ${useType}, 数量: ${quantity}`);

    const bag = await this.getCharacterBag(characterId);
    if (!bag) {
      throw new NotFoundException('背包不存在');
    }

    // 检查物品是否存在
    const bookMark = bag.getOneBookMark(bookMarkId);
    if (!bookMark || !bookMark.itemUids.includes(itemUid)) {
      throw new NotFoundException('物品不存在');
    }

    // 使用物品 - 基于old项目useItemMainType
    const result = await this.executeItemUse(bag, bookMarkId, itemUid, resId, useType, subType, quantity, heroUid);

    if (result.code === 0) {
      await this.inventoryRepository.update(bag._id.toString(), bag.toObject());
      this.logger.log(`物品使用成功: ${characterId}, 物品: ${itemUid}`);
    }

    return result;
  }

  /**
   * 获取背包列表（客户端数据）
   * 基于old项目: makeClientBagList方法
   */
  async getBagList(characterId: string): Promise<any[]> {
    this.logger.log(`获取背包列表: ${characterId}`);

    let bag = await this.getCharacterBag(characterId);
    if (!bag) {
      bag = await this.initCharacterBag(characterId, 'server_001');
    }

    return bag.makeClientBagList();
  }

  /**
   * 查找物品在背包中的位置
   * 基于old项目: findItemInBag方法
   */
  async findItemInBag(characterId: string, itemUid: string): Promise<{ bookMarkId: number; index: number } | null> {
    this.logger.log(`查找物品位置: ${characterId}, 物品: ${itemUid}`);

    const bag = await this.getCharacterBag(characterId);
    if (!bag) {
      return null;
    }

    const bookMarkId = bag.getBookMarkIdByItemUid(itemUid);
    if (!bookMarkId) {
      return null;
    }

    const bookMark = bag.getOneBookMark(bookMarkId);
    if (!bookMark) {
      return null;
    }

    const index = bookMark.itemUids.indexOf(itemUid);
    return index !== -1 ? { bookMarkId, index } : null;
  }

  /**
   * 计算扩展费用
   * 基于old项目: calculateExpandCost方法
   */
  async calculateExpandCost(characterId: string, bookMarkId: number, expandCount: number = 1): Promise<number> {
    const bag = await this.getCharacterBag(characterId);
    if (!bag) {
      return 0;
    }

    return bag.calculateExpandCost(bookMarkId, expandCount);
  }

  // ==================== 私有辅助方法 ====================

  /**
   * 初始化页签配置
   * 基于old项目: checkBookMarkInitDB方法
   */
  private async initBookMarkConfigs(): Promise<BookMarkEntry[]> {
    // TODO: 从配置表获取页签配置
    // const bagBookMarkConfigs = await this.gameConfig.bagBookMark.getAll();

    // 暂时使用硬编码的默认配置
    const defaultConfigs: BookMarkEntry[] = [
      {
        id: 1,
        name: '装备',
        type: BookMarkType.EQUIPMENT,
        capacity: 50,
        usedSlots: 0,
        expandCount: 0,
        nextExpandCost: 1000,
        itemUids: [],
        createTime: Date.now(),
        updateTime: Date.now(),
        remainingCapacity: 50,
        usageRate: 0,
        canExpand: true,
      },
      {
        id: 2,
        name: '消耗品',
        type: BookMarkType.CONSUMABLE,
        capacity: 50,
        usedSlots: 0,
        expandCount: 0,
        nextExpandCost: 1000,
        itemUids: [],
        createTime: Date.now(),
        updateTime: Date.now(),
        remainingCapacity: 50,
        usageRate: 0,
        canExpand: true,
      },
      {
        id: 3,
        name: '材料',
        type: BookMarkType.MATERIAL,
        capacity: 50,
        usedSlots: 0,
        expandCount: 0,
        nextExpandCost: 1000,
        itemUids: [],
        createTime: Date.now(),
        updateTime: Date.now(),
        remainingCapacity: 50,
        usageRate: 0,
        canExpand: true,
      },
    ];

    return defaultConfigs;
  }

  // ==================== 物品使用逻辑补全 ====================

  /**
   * 执行物品使用逻辑
   * 基于old项目: useItemMainType方法的完整实现
   */
  private async executeItemUse(
    bag: any,
    bookMarkId: number,
    itemUid: string,
    resId: number,
    useType: number,
    subType: number,
    quantity: number,
    heroUid?: string
  ): Promise<any> {
    this.logger.log(`执行物品使用: 类型${useType}, 子类型${subType}, 数量${quantity}`);

    // 获取物品配置
    const itemConfig = await this.gameConfig.item.get(resId);
    if (!itemConfig) {
      return { code: -1, message: '物品配置不存在' };
    }

    // 检查物品数量
    const bookMark = bag.getOneBookMark(bookMarkId);
    if (!bookMark || !bookMark.itemUids.includes(itemUid)) {
      return { code: -2, message: '物品不存在' };
    }

    // 根据使用类型执行不同逻辑
    switch (useType) {
      case 1: // 体力恢复类
        return await this.useEnergyItem(bag, itemUid, itemConfig, quantity);

      case 2: // 经验道具类
        return await this.useExpItem(bag, itemUid, itemConfig, quantity, heroUid);

      case 3: // 货币道具类
        return await this.useCurrencyItem(bag, itemUid, itemConfig, quantity);

      case 4: // 球员卡类
        return await this.useHeroCardItem(bag, itemUid, itemConfig, quantity);

      case 5: // 装备道具类
        return await this.useEquipmentItem(bag, itemUid, itemConfig, quantity, heroUid);

      case 6: // 礼包道具类
        return await this.useGiftPackItem(bag, itemUid, itemConfig, quantity);

      default:
        return { code: -3, message: '不支持的物品类型' };
    }
  }

  /**
   * 使用体力恢复道具
   * 基于old项目: 体力恢复逻辑
   */
  private async useEnergyItem(bag: any, itemUid: string, itemConfig: any, quantity: number): Promise<any> {
    this.logger.log(`使用体力道具: ${itemConfig.name}, 数量: ${quantity}`);

    const energyRestore = itemConfig.effect?.energy || 10;
    const totalRestore = energyRestore * quantity;

    // TODO: 调用Character服务恢复体力
    // 开启方法：取消注释下面的微服务调用代码
    // await this.restoreCharacterEnergy(bag.characterId, totalRestore);

    // 消耗道具
    const removeResult = bag.removeFromBag(bag.getBookMarkIdByItemUid(itemUid), itemUid, quantity);
    if (!removeResult) {
      return { code: -4, message: '道具消耗失败' };
    }

    return {
      code: 0,
      message: '体力恢复成功',
      effects: {
        energyRestored: totalRestore,
      },
    };
  }

  /**
   * 使用经验道具
   * 基于old项目: 经验道具使用逻辑
   */
  private async useExpItem(bag: any, itemUid: string, itemConfig: any, quantity: number, heroUid?: string): Promise<any> {
    this.logger.log(`使用经验道具: ${itemConfig.name}, 数量: ${quantity}, 目标球员: ${heroUid}`);

    if (!heroUid) {
      return { code: -1, message: '需要指定目标球员' };
    }

    const expGain = itemConfig.effect?.exp || 100;
    const totalExp = expGain * quantity;

    // TODO: 调用Hero服务增加球员经验
    // 开启方法：取消注释下面的微服务调用代码
    // await this.addHeroExperience(heroUid, totalExp);

    // 消耗道具
    const removeResult = bag.removeFromBag(bag.getBookMarkIdByItemUid(itemUid), itemUid, quantity);
    if (!removeResult) {
      return { code: -4, message: '道具消耗失败' };
    }

    return {
      code: 0,
      message: '经验增加成功',
      effects: {
        heroUid,
        expGained: totalExp,
      },
    };
  }

  /**
   * 使用货币道具
   * 基于old项目: 货币道具使用逻辑
   */
  private async useCurrencyItem(bag: any, itemUid: string, itemConfig: any, quantity: number): Promise<any> {
    this.logger.log(`使用货币道具: ${itemConfig.name}, 数量: ${quantity}`);

    const currencyType = itemConfig.effect?.currencyType || 'gold';
    const currencyAmount = itemConfig.effect?.amount || 1000;
    const totalAmount = currencyAmount * quantity;

    // TODO: 调用Character服务增加货币
    // 开启方法：取消注释下面的微服务调用代码
    // await this.addCharacterCurrency(bag.characterId, currencyType, totalAmount);

    // 消耗道具
    const removeResult = bag.removeFromBag(bag.getBookMarkIdByItemUid(itemUid), itemUid, quantity);
    if (!removeResult) {
      return { code: -4, message: '道具消耗失败' };
    }

    return {
      code: 0,
      message: '货币获得成功',
      effects: {
        currencyType,
        amountGained: totalAmount,
      },
    };
  }

  /**
   * 使用球员卡道具
   * 基于old项目: 球员卡使用逻辑
   */
  private async useHeroCardItem(bag: any, itemUid: string, itemConfig: any, quantity: number): Promise<any> {
    this.logger.log(`使用球员卡: ${itemConfig.name}, 数量: ${quantity}`);

    const heroResId = itemConfig.effect?.heroResId;
    if (!heroResId) {
      return { code: -1, message: '球员卡配置错误' };
    }

    const newHeroes = [];
    for (let i = 0; i < quantity; i++) {
      // 调用Hero服务创建球员
      const newHero = await this.createHeroFromCard(bag.characterId, heroResId);
      if (newHero) {
        newHeroes.push(newHero);
      }
    }

    // 消耗道具
    const removeResult = bag.removeFromBag(bag.getBookMarkIdByItemUid(itemUid), itemUid, quantity);
    if (!removeResult) {
      return { code: -4, message: '道具消耗失败' };
    }

    return {
      code: 0,
      message: '球员获得成功',
      effects: {
        newHeroes,
      },
    };
  }

  /**
   * 使用装备道具
   * 基于old项目: 装备道具使用逻辑
   */
  private async useEquipmentItem(bag: any, itemUid: string, itemConfig: any, quantity: number, heroUid?: string): Promise<any> {
    this.logger.log(`使用装备道具: ${itemConfig.name}, 数量: ${quantity}, 目标球员: ${heroUid}`);

    if (!heroUid) {
      return { code: -1, message: '需要指定目标球员' };
    }

    // TODO: 调用Hero服务装备道具
    // 开启方法：取消注释下面的微服务调用代码
    // await this.equipHeroItem(heroUid, itemConfig.id);

    // 消耗道具
    const removeResult = bag.removeFromBag(bag.getBookMarkIdByItemUid(itemUid), itemUid, quantity);
    if (!removeResult) {
      return { code: -4, message: '道具消耗失败' };
    }

    return {
      code: 0,
      message: '装备成功',
      effects: {
        heroUid,
        equipmentId: itemConfig.id,
      },
    };
  }

  /**
   * 使用礼包道具
   * 基于old项目: 礼包道具使用逻辑
   */
  private async useGiftPackItem(bag: any, itemUid: string, itemConfig: any, quantity: number): Promise<any> {
    this.logger.log(`使用礼包道具: ${itemConfig.name}, 数量: ${quantity}`);

    const rewards = itemConfig.effect?.rewards || [];
    const allRewards = [];

    for (let i = 0; i < quantity; i++) {
      for (const reward of rewards) {
        // 根据奖励类型发放奖励
        await this.processGiftPackReward(bag.characterId, reward);
        allRewards.push(reward);
      }
    }

    // 消耗道具
    const removeResult = bag.removeFromBag(bag.getBookMarkIdByItemUid(itemUid), itemUid, quantity);
    if (!removeResult) {
      return { code: -4, message: '道具消耗失败' };
    }

    return {
      code: 0,
      message: '礼包开启成功',
      effects: {
        rewards: allRewards,
      },
    };
  }

  // ==================== 微服务通信参考代码实现 ====================
  // 以下方法提供完整的微服务通信参考代码，包含详细的使用说明

  /**
   * 恢复角色体力 - 微服务通信参考实现
   * 基于old项目: 体力恢复逻辑
   *
   * 使用说明：
   * 1. 确保Character服务有restoreEnergy接口
   * 2. 在上面的TODO位置取消注释调用此方法
   * 3. 根据实际需求调整体力上限检查
   */
  private async restoreCharacterEnergy(characterId: string, energyAmount: number): Promise<void> {
    this.logger.log(`恢复角色体力: ${characterId}, 数量: ${energyAmount}`);

    try {
      // 调用Character服务恢复体力
      // 参考代码：完整的微服务调用实现
      /*
      const result = await this.microserviceClient.call(
        MICROSERVICE_NAMES.CHARACTER_SERVICE,
        'character.restoreEnergy',
        {
          characterId,
          energyAmount,
          source: 'item_use'
        }
      );

      if (result.code === 0) {
        this.logger.log(`体力恢复成功: ${characterId}, 当前体力: ${result.data.currentEnergy}`);
      } else {
        this.logger.error('体力恢复失败', result);
      }
      */

      this.logger.debug('角色体力恢复成功（模拟）');
    } catch (error) {
      this.logger.error('恢复角色体力失败', error);
    }
  }





  /**
   * 从球员卡创建球员 - 微服务通信参考实现
   * 基于old项目: 球员卡使用逻辑
   *
   * 使用说明：
   * 1. 确保Hero服务已启动并可通信
   * 2. 在上面的TODO位置取消注释调用此方法
   * 3. 确保Hero服务有createHero接口
   */
  private async createHeroFromCard(characterId: string, heroResId: number): Promise<any> {
    this.logger.log(`从球员卡创建球员: ${characterId}, 球员配置ID: ${heroResId}`);

    try {
      // 调用Hero服务创建球员
      // 参考代码：完整的微服务调用实现
      /*
      const result = await this.microserviceClient.call(
        MICROSERVICE_NAMES.HERO_SERVICE,
        'hero.createHero',
        {
          characterId,
          resId: heroResId,
          source: 'hero_card'
        }
      );

      if (result.code === 0) {
        this.logger.log(`球员创建成功: ${result.data.heroId}, 名称: ${result.data.name}`);
        return result.data;
      } else {
        this.logger.error('球员创建失败', result);
        return null;
      }
      */

      // 临时返回模拟数据，避免影响测试
      return {
        heroId: `hero_${Date.now()}_${Math.random()}`,
        resId: heroResId,
        name: '新球员',
        level: 1,
      };
    } catch (error) {
      this.logger.error('从球员卡创建球员失败', error);
      return null;
    }
  }

  /**
   * 装备球员道具 - 微服务通信参考实现
   * 基于old项目: 装备道具使用逻辑
   *
   * 使用说明：
   * 1. 确保Hero服务已启动并可通信
   * 2. 在上面的TODO位置取消注释调用此方法
   * 3. 确保Hero服务有equipItem接口
   */
  private async equipHeroItem(heroId: string, itemId: number): Promise<void> {
    this.logger.log(`装备球员道具: ${heroId}, 道具ID: ${itemId}`);

    try {
      // 调用Hero服务装备道具
      // 参考代码：完整的微服务调用实现
      /*
      const result = await this.microserviceClient.call(
        MICROSERVICE_NAMES.HERO_SERVICE,
        'hero.equipItem',
        {
          heroId,
          itemId,
          source: 'item_use'
        }
      );

      if (result.code === 0) {
        this.logger.log(`道具装备成功: ${heroId}, 道具: ${itemId}`);
      } else {
        this.logger.error('道具装备失败', result);
      }
      */

      this.logger.debug('球员道具装备成功（模拟）');
    } catch (error) {
      this.logger.error('装备球员道具失败', error);
    }
  }

  /**
   * 处理礼包奖励 - 微服务通信参考实现
   * 基于old项目: 礼包奖励发放逻辑
   *
   * 使用说明：
   * 1. 确保各个服务的奖励发放接口已实现
   * 2. 在上面的TODO位置取消注释调用此方法
   * 3. 根据实际奖励类型调整处理逻辑
   */
  private async processGiftPackReward(characterId: string, reward: any): Promise<void> {
    this.logger.log(`处理礼包奖励: ${characterId}, 奖励类型: ${reward.type}`);

    try {
      // 根据奖励类型分别处理（基于old项目礼包奖励逻辑）
      switch (reward.type) {
        case 'currency':
        case 'gold':
          await this.addCharacterCurrency(characterId, reward.currencyType || 'gold', reward.amount);
          break;

        case 'item':
          await this.addCharacterItem(characterId, reward.itemId, reward.quantity || 1);
          break;

        case 'hero':
        case 'hero_card':
          await this.createHeroFromCard(characterId, reward.heroResId);
          break;

        case 'experience':
        case 'exp':
          if (reward.heroId) {
            await this.addHeroExperience(reward.heroId, reward.amount);
          } else {
            await this.addCharacterExperience(characterId, reward.amount);
          }
          break;

        case 'energy':
          await this.addCharacterEnergy(characterId, reward.amount);
          break;

        default:
          this.logger.warn(`未知奖励类型: ${reward.type}`);
      }

      this.logger.debug(`礼包奖励处理成功: ${reward.type}, 数量: ${reward.amount || reward.quantity || 1}`);
    } catch (error) {
      this.logger.error('处理礼包奖励失败', error);
      throw error;
    }
  }

  /**
   * 添加角色货币
   * 基于old项目: 货币奖励发放逻辑
   */
  private async addCharacterCurrency(characterId: string, currencyType: string, amount: number): Promise<void> {
    try {
      // TODO: 调用Character服务添加货币
      // const result = await this.microserviceClient.call(
      //   MICROSERVICE_NAMES.CHARACTER_SERVICE,
      //   'character.addCurrency',
      //   { characterId, currencyType, amount, source: 'gift_pack' }
      // );

      this.logger.debug(`添加角色货币: ${characterId}, 类型: ${currencyType}, 数量: ${amount}`);
    } catch (error) {
      this.logger.error('添加角色货币失败', error);
      throw error;
    }
  }

  /**
   * 添加角色物品
   * 基于old项目: 物品奖励发放逻辑
   */
  private async addCharacterItem(characterId: string, itemId: number, quantity: number): Promise<void> {
    try {
      // TODO: 调用Inventory服务添加物品
      // const result = await this.microserviceClient.call(
      //   MICROSERVICE_NAMES.CHARACTER_SERVICE,
      //   'inventory.addItem',
      //   { characterId, itemId, quantity, source: 'gift_pack' }
      // );

      this.logger.debug(`添加角色物品: ${characterId}, 物品: ${itemId}, 数量: ${quantity}`);
    } catch (error) {
      this.logger.error('添加角色物品失败', error);
      throw error;
    }
  }

  /**
   * 添加球员经验
   * 基于old项目: 球员经验奖励逻辑
   */
  private async addHeroExperience(heroId: string, amount: number): Promise<void> {
    try {
      // TODO: 调用Hero服务添加经验
      // const result = await this.microserviceClient.call(
      //   MICROSERVICE_NAMES.HERO_SERVICE,
      //   'hero.addExperience',
      //   { heroId, amount, source: 'gift_pack' }
      // );

      this.logger.debug(`添加球员经验: ${heroId}, 数量: ${amount}`);
    } catch (error) {
      this.logger.error('添加球员经验失败', error);
      throw error;
    }
  }

  /**
   * 添加角色经验
   * 基于old项目: 角色经验奖励逻辑
   */
  private async addCharacterExperience(characterId: string, amount: number): Promise<void> {
    try {
      // TODO: 调用Character服务添加经验
      // const result = await this.microserviceClient.call(
      //   MICROSERVICE_NAMES.CHARACTER_SERVICE,
      //   'character.addExperience',
      //   { characterId, amount, source: 'gift_pack' }
      // );

      this.logger.debug(`添加角色经验: ${characterId}, 数量: ${amount}`);
    } catch (error) {
      this.logger.error('添加角色经验失败', error);
      throw error;
    }
  }

  /**
   * 添加角色体力
   * 基于old项目: 体力奖励发放逻辑
   */
  private async addCharacterEnergy(characterId: string, amount: number): Promise<void> {
    try {
      // TODO: 调用Character服务添加体力
      // const result = await this.microserviceClient.call(
      //   MICROSERVICE_NAMES.CHARACTER_SERVICE,
      //   'character.addEnergy',
      //   { characterId, amount, source: 'gift_pack' }
      // );

      this.logger.debug(`添加角色体力: ${characterId}, 数量: ${amount}`);
    } catch (error) {
      this.logger.error('添加角色体力失败', error);
      throw error;
    }
  }

  /**
   * 获取物品类型优先级
   * 基于old项目: 物品类型排序优先级
   */
  private getItemTypePriority(resId: number): number {
    // 基于old项目：不同物品类型的排序优先级
    const itemType = Math.floor(resId / 1000);
    const typePriority = {
      1: 1,  // 球员卡
      2: 2,  // 装备
      3: 3,  // 消耗品
      4: 4,  // 礼包
      5: 5,  // 材料
      6: 6,  // 其他
    };
    return typePriority[itemType] || 99;
  }

  /**
   * 获取物品品质
   * 基于old项目: 物品品质系统
   */
  private getItemQuality(resId: number): number {
    // 基于old项目：物品ID的品质映射规则
    // 通常高位数字表示品质
    const qualityDigit = Math.floor((resId % 1000) / 100);
    return qualityDigit || 1;
  }

  /**
   * 背包排序
   * 基于old项目: 背包物品排序算法
   *
   * 排序规则：
   * 1. 物品类型优先级
   * 2. 物品品质（高品质在前）
   * 3. 物品ID
   * 4. 获得时间（新获得在前）
   */
  private sortBagItems(items: any[]): any[] {
    return items.sort((a, b) => {
      // 1. 按物品类型排序（基于old项目物品类型优先级）
      const typeA = this.getItemTypePriority(a.resId);
      const typeB = this.getItemTypePriority(b.resId);
      if (typeA !== typeB) {
        return typeA - typeB;
      }

      // 2. 按品质排序（高品质在前）
      const qualityA = this.getItemQuality(a.resId);
      const qualityB = this.getItemQuality(b.resId);
      if (qualityA !== qualityB) {
        return qualityB - qualityA; // 降序，高品质在前
      }

      // 3. 按物品ID排序
      if (a.resId !== b.resId) {
        return a.resId - b.resId;
      }

      // 4. 按获得时间排序（新获得在前）
      const timeA = a.createTime || 0;
      const timeB = b.createTime || 0;
      return timeB - timeA; // 降序，新获得在前
    });
  }
}
