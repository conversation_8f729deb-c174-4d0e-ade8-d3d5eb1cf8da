import { SetMetadata, createParamDecorator, ExecutionContext } from '@nestjs/common';
import { Request } from 'express';

/**
 * Gateway 服务通用装饰器
 */

// 角色装饰器
export const Roles = (...roles: string[]) => SetMetadata('roles', roles);

// 权限装饰器
export const Permissions = (...permissions: string[]) => SetMetadata('permissions', permissions);

// 公开接口装饰器（跳过认证）
export const Public = () => SetMetadata('isPublic', true);

// 限流装饰器
export const RateLimit = (options: {
  windowMs?: number;
  max?: number;
  skipSuccessfulRequests?: boolean;
  skipFailedRequests?: boolean;
}) => SetMetadata('rateLimit', options);

// 缓存装饰器
export const Cacheable = (options: {
  ttl?: number;
  key?: string;
  tags?: string[];
}) => SetMetadata('cache', options);

// 审计日志装饰器
export const AuditLog = (action: string, resource?: string) => 
  SetMetadata('auditLog', { action, resource });

// 获取当前用户装饰器
export const CurrentUser = createParamDecorator(
  (data: unknown, ctx: ExecutionContext) => {
    const request = ctx.switchToHttp().getRequest<Request>();
    return (request as any).user;
  },
);

// 获取请求 IP 装饰器
export const ClientIp = createParamDecorator(
  (data: unknown, ctx: ExecutionContext) => {
    const request = ctx.switchToHttp().getRequest<Request>();
    return (
      request.get('X-Forwarded-For')?.split(',')[0]?.trim() ||
      request.get('X-Real-IP') ||
      request.get('X-Client-IP') ||
      request.connection.remoteAddress ||
      request.socket.remoteAddress ||
      '127.0.0.1'
    );
  },
);

// 获取请求 ID 装饰器
export const RequestId = createParamDecorator(
  (data: unknown, ctx: ExecutionContext) => {
    const request = ctx.switchToHttp().getRequest<Request>();
    return (request as any).requestId;
  },
);

// 获取认证上下文装饰器
export const AuthContext = createParamDecorator(
  (data: unknown, ctx: ExecutionContext) => {
    const request = ctx.switchToHttp().getRequest<Request>();
    return (request as any).auth;
  },
);

// 获取用户代理装饰器
export const UserAgent = createParamDecorator(
  (data: unknown, ctx: ExecutionContext) => {
    const request = ctx.switchToHttp().getRequest<Request>();
    return request.get('User-Agent');
  },
);

// API 版本装饰器
export const ApiVersion = (version: string) => SetMetadata('version', version);

// 超时装饰器
export const Timeout = (ms: number) => SetMetadata('timeout', ms);

// 重试装饰器
export const Retry = (options: {
  attempts?: number;
  delay?: number;
  backoff?: 'fixed' | 'exponential';
}) => SetMetadata('retry', options);

// 熔断器装饰器
export const CircuitBreaker = (options: {
  threshold?: number;
  timeout?: number;
  resetTimeout?: number;
}) => SetMetadata('circuitBreaker', options);

// 负载均衡装饰器
export const LoadBalance = (strategy: 'round-robin' | 'weighted' | 'least-connections') => 
  SetMetadata('loadBalance', strategy);

// 服务发现装饰器
export const ServiceDiscovery = (serviceName: string) => 
  SetMetadata('serviceDiscovery', serviceName);

// 指标收集装饰器
export const Metrics = (options: {
  name?: string;
  labels?: Record<string, string>;
  histogram?: boolean;
  counter?: boolean;
}) => SetMetadata('metrics', options);

// 链路追踪装饰器
export const Trace = (operationName?: string) => 
  SetMetadata('trace', { operationName });

// 健康检查装饰器
export const HealthCheck = (name: string) => SetMetadata('healthCheck', name);

// 配置注入装饰器
export const ConfigValue = (key: string, defaultValue?: any) => 
  SetMetadata('configValue', { key, defaultValue });

// 异步锁装饰器
export const Lock = (key: string, ttl?: number) => 
  SetMetadata('lock', { key, ttl });

// 事件发布装饰器
export const EventPublish = (eventType: string) => 
  SetMetadata('eventPublish', eventType);

// 事件订阅装饰器
export const EventSubscribe = (eventType: string) =>
  SetMetadata('eventSubscribe', eventType);

// 导出Token作用域装饰器
export * from './token-scope.decorator';
