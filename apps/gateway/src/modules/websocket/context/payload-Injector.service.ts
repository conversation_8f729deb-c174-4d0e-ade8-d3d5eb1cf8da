import { Injectable, Logger } from '@nestjs/common';
import { InjectedContext } from '@common/types';

// 上下文服务
import {AuthenticatedSocket} from "@gateway/modules/websocket/gateways/websocket.gateway";

// 新的增强Payload接口
export interface EnhancedPayload {
  // 原始业务数据
  [key: string]: any;

  // 🎯 统一的注入上下文
  injectedContext: InjectedContext;
}

/**
 * Payload注入服务
 * 负责增强WebSocket消息的payload，注入各种上下文信息
 * 
 * 核心功能：
 * 1. 用户上下文注入：注入用户ID和基础信息
 * 2. 区服上下文注入：注入区服和角色相关信息
 * 3. 路由上下文注入：注入路由策略和消息类型信息
 * 4. 跨服上下文注入：为跨服消息注入特殊上下文
 * 5. 全服上下文注入：为全服消息注入广播上下文
 */
@Injectable()
export class PayloadInjectorService {
  private readonly logger = new Logger(PayloadInjectorService.name);

  constructor(
  ) {}

  /**
   * 增强payload，注入各种上下文信息
   * 主要入口方法
   *
   * @param originalPayload 原始payload
   * @param client 客户端上下文
   * @param routingStrategy 路由策略
   * @param messageId 消息ID
   */
  async injectPayload(
    originalPayload: any,
    client: AuthenticatedSocket,
    routingStrategy: string = 'normal',
    messageId?: string
  ): Promise<EnhancedPayload> {
    this.logger.debug(`🔧 Enhancing payload for user ${client.userId}, strategy: ${routingStrategy}`);

    // 🔒 移除客户端可能伪造的注入上下文
    const cleanPayload = this.removeClientInjectedContext(originalPayload);

    try {
      // 🔒 基于认证信息构建可信的上下文
      const trustedContext: InjectedContext = {
        userId: client.userId,
        wsContext: {
          timestamp: Date.now(),
          routingStrategy,
          messageId,
        },
        requestId: this.generateRequestId(),
      };

      // 注入区服上下文（如果有角色Token）
      await this.injectServerContext(trustedContext, client);

      // 🎯 构建新的增强payload结构
      const enhancedPayload: EnhancedPayload = {
        ...cleanPayload,
        trustedContext,
      };

      this.logger.debug(`✅ Payload enhanced successfully`);
      return enhancedPayload;

    } catch (error) {
      this.logger.error(`❌ Failed to enhance payload:`, error);

      // 返回基础增强payload
      return {
        ...cleanPayload,
        injectedContext: {
          userId: client.userId,
          wsContext: {
            timestamp: Date.now(),
            routingStrategy,
            messageId,
          },
          requestId: this.generateRequestId(),
        },
      };
    }
  }

  /**
   * 生成请求ID
   */
  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  /**
   * 移除客户端可能伪造的注入上下文
   */
  private removeClientInjectedContext(payload: any): any {
    const { injectedContext, ...cleanPayload } = payload;
    return cleanPayload;
  }

  /**
   * 注入区服上下文
   */
  private async injectServerContext(
    injectedContext: InjectedContext,
    client?: AuthenticatedSocket
  ): Promise<void> {
    if (!client?.metadata) {
      this.logger.debug(`📍 No metadata found, skipping server context injection`);
      return;
    }

    try {
      const metadata = client.metadata;
      // 🎯 注入到injectedContext
      injectedContext.serverContext = {
        serverId: metadata.serverId,
        characterId: metadata.characterId
      };

      this.logger.debug(`📍 Server context injected: ${JSON.stringify(injectedContext.serverContext)}`);

    } catch (error) {
      this.logger.error(`❌ Failed to inject server context:`, error);
    }
  }

  /**
   * 根据路由策略注入特殊上下文
   */
  private async injectRoutingContext(
    payload: EnhancedPayload,
    routingStrategy: string,
    client?: AuthenticatedSocket
  ): Promise<void> {
    switch (routingStrategy) {
      case 'cross_server':
        await this.injectCrossServerContext(payload, client);
        break;
      case 'global':
        await this.injectGlobalContext(payload, client);
        break;
      case 'system':
        await this.injectSystemContext(payload, client);
        break;
      default:
        // 普通消息不需要特殊上下文
        break;
    }
  }

  /**
   * 注入跨服上下文
   */
  private async injectCrossServerContext(
    payload: EnhancedPayload,
    client?: AuthenticatedSocket
  ): Promise<void> {
    try {
      payload.crossServerContext = {
        sourceServerId: payload.serverContext?.serverId,
        messageType: 'cross_server',
        targetServers: [], // 可以根据具体业务逻辑设置目标区服
      };

      // 添加跨服标记到payload根级别
      payload._crossServer = true;
      payload._sourceServer = payload.serverContext?.serverId;
      payload._messageType = 'cross_server';

      this.logger.debug(`🌐 Cross-server context injected`);

    } catch (error) {
      this.logger.error(`❌ Failed to inject cross-server context:`, error);
    }
  }

  /**
   * 注入全服上下文
   */
  private async injectGlobalContext(
    payload: EnhancedPayload,
    client?: AuthenticatedSocket
  ): Promise<void> {
    try {
      payload.globalContext = {
        messageType: 'global',
        allServers: true,
        priority: this.determineGlobalMessagePriority(payload),
      };

      // 添加全服标记到payload根级别
      payload._global = true;
      payload._allServers = true;
      payload._messageType = 'global';

      this.logger.debug(`🌍 Global context injected`);

    } catch (error) {
      this.logger.error(`❌ Failed to inject global context:`, error);
    }
  }

  /**
   * 注入系统上下文
   */
  private async injectSystemContext(
    payload: EnhancedPayload,
    client?: AuthenticatedSocket
  ): Promise<void> {
    try {
      // 系统消息不需要区服上下文，移除相关信息
      delete payload.serverContext;

      // 添加系统标记
      payload._system = true;
      payload._messageType = 'system';

      this.logger.debug(`⚙️ System context injected`);

    } catch (error) {
      this.logger.error(`❌ Failed to inject system context:`, error);
    }
  }

  /**
   * 批量增强多个payload
   */
  async injectPayloads(
    payloads: Array<{
      payload: any;
      userId: string;
      client?: any;
      routingStrategy?: string;
      messageId?: string;
    }>
  ): Promise<EnhancedPayload[]> {
    this.logger.debug(`🔧 Batch enhancing ${payloads.length} payloads`);

    const results = await Promise.allSettled(
      payloads.map(item =>
        this.injectPayload(
          item.payload,
          item.client,
          item.routingStrategy,
          item.messageId
        )
      )
    );

    const successful = results
      .filter(r => r.status === 'fulfilled')
      .map(r => (r as PromiseFulfilledResult<EnhancedPayload>).value);

    const failed = results.filter(r => r.status === 'rejected').length;

    this.logger.debug(`📊 Batch enhancement completed: ${successful.length} successful, ${failed} failed`);

    return successful;
  }

  /**
   * 确定全服消息的优先级
   */
  private determineGlobalMessagePriority(payload: any): number {
    // 根据消息内容确定优先级
    // 这里可以实现复杂的优先级算法
    if (payload.emergency) return 5;
    if (payload.urgent) return 4;
    if (payload.important) return 3;
    return 2; // 默认优先级
  }
}