import { Injectable, Logger } from '@nestjs/common';
import { InjectedContext } from '@common/types';

// 上下文服务
import { ServerContextService, ServerContext } from './server-context.service';

// 新的增强Payload接口
export interface EnhancedPayload {
  // 原始业务数据
  [key: string]: any;

  // 🎯 统一的注入上下文
  injectedContext: InjectedContext;
}

/**
 * Payload增强服务
 * 负责增强WebSocket消息的payload，注入各种上下文信息
 * 
 * 核心功能：
 * 1. 用户上下文注入：注入用户ID和基础信息
 * 2. 区服上下文注入：注入区服和角色相关信息
 * 3. 路由上下文注入：注入路由策略和消息类型信息
 * 4. 跨服上下文注入：为跨服消息注入特殊上下文
 * 5. 全服上下文注入：为全服消息注入广播上下文
 */
@Injectable()
export class PayloadEnhancerService {
  private readonly logger = new Logger(PayloadEnhancerService.name);

  constructor(
    private readonly serverContextService: ServerContextService,
  ) {}

  /**
   * 增强payload，注入各种上下文信息
   * 主要入口方法
   *
   * @param originalPayload 原始payload
   * @param userId 用户ID
   * @param clientContext 客户端上下文
   * @param routingStrategy 路由策略
   * @param messageId 消息ID
   */
  async enhancePayload(
    originalPayload: any,
    userId: string,
    clientContext?: any,
    routingStrategy: string = 'normal',
    messageId?: string
  ): Promise<EnhancedPayload> {
    this.logger.debug(`🔧 Enhancing payload for user ${userId}, strategy: ${routingStrategy}`);

    try {
      // 🎯 构建注入上下文
      const injectedContext: InjectedContext = {
        userId,
        wsContext: {
          timestamp: Date.now(),
          routingStrategy,
          messageId,
        },
        requestId: this.generateRequestId(),
        clientVersion: clientContext?.clientVersion,
      };

      // 注入区服上下文（如果有角色Token）
      await this.injectServerContext(injectedContext, clientContext);

      // 🎯 构建新的增强payload结构
      const enhancedPayload: EnhancedPayload = {
        ...originalPayload,
        injectedContext,
      };

      this.logger.debug(`✅ Payload enhanced successfully`);
      return enhancedPayload;

    } catch (error) {
      this.logger.error(`❌ Failed to enhance payload:`, error);

      // 返回基础增强payload
      return {
        ...originalPayload,
        injectedContext: {
          userId,
          wsContext: {
            timestamp: Date.now(),
            routingStrategy,
            messageId,
          },
          requestId: this.generateRequestId(),
        },
      };
    }
  }

  /**
   * 生成请求ID
   */
  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 注入区服上下文
   */
  private async injectServerContext(
    injectedContext: InjectedContext,
    clientContext?: any
  ): Promise<void> {
    if (!clientContext?.character) {
      this.logger.debug(`📍 No character context found, skipping server context injection`);
      return;
    }

    try {
      // 提取基础服务器上下文
      const baseContext = this.serverContextService.extractServerContext(clientContext);
      if (!baseContext) {
        return;
      }

      // 获取增强的服务器上下文
      const extendedContext = await this.serverContextService.getExtendedServerContext(baseContext);

      // 🎯 注入到injectedContext
      injectedContext.serverContext = {
        serverId: extendedContext.serverId,
        characterId: extendedContext.characterId,
        characterLevel: extendedContext.characterLevel,
        characterName: extendedContext.characterName,
      };

      this.logger.debug(`📍 Server context injected: ${JSON.stringify(injectedContext.serverContext)}`);

    } catch (error) {
      this.logger.error(`❌ Failed to inject server context:`, error);
    }
  }

  /**
   * 根据路由策略注入特殊上下文
   */
  private async injectRoutingContext(
    payload: EnhancedPayload,
    routingStrategy: string,
    clientContext?: any
  ): Promise<void> {
    switch (routingStrategy) {
      case 'cross_server':
        await this.injectCrossServerContext(payload, clientContext);
        break;
      case 'global':
        await this.injectGlobalContext(payload, clientContext);
        break;
      case 'system':
        await this.injectSystemContext(payload, clientContext);
        break;
      default:
        // 普通消息不需要特殊上下文
        break;
    }
  }

  /**
   * 注入跨服上下文
   */
  private async injectCrossServerContext(
    payload: EnhancedPayload,
    clientContext?: any
  ): Promise<void> {
    try {
      payload.crossServerContext = {
        sourceServerId: payload.serverContext?.serverId,
        messageType: 'cross_server',
        targetServers: [], // 可以根据具体业务逻辑设置目标区服
      };

      // 添加跨服标记到payload根级别
      payload._crossServer = true;
      payload._sourceServer = payload.serverContext?.serverId;
      payload._messageType = 'cross_server';

      this.logger.debug(`🌐 Cross-server context injected`);

    } catch (error) {
      this.logger.error(`❌ Failed to inject cross-server context:`, error);
    }
  }

  /**
   * 注入全服上下文
   */
  private async injectGlobalContext(
    payload: EnhancedPayload,
    clientContext?: any
  ): Promise<void> {
    try {
      payload.globalContext = {
        messageType: 'global',
        allServers: true,
        priority: this.determineGlobalMessagePriority(payload),
      };

      // 添加全服标记到payload根级别
      payload._global = true;
      payload._allServers = true;
      payload._messageType = 'global';

      this.logger.debug(`🌍 Global context injected`);

    } catch (error) {
      this.logger.error(`❌ Failed to inject global context:`, error);
    }
  }

  /**
   * 注入系统上下文
   */
  private async injectSystemContext(
    payload: EnhancedPayload,
    clientContext?: any
  ): Promise<void> {
    try {
      // 系统消息不需要区服上下文，移除相关信息
      delete payload.serverContext;

      // 添加系统标记
      payload._system = true;
      payload._messageType = 'system';

      this.logger.debug(`⚙️ System context injected`);

    } catch (error) {
      this.logger.error(`❌ Failed to inject system context:`, error);
    }
  }

  /**
   * 批量增强多个payload
   */
  async enhancePayloads(
    payloads: Array<{
      payload: any;
      userId: string;
      clientContext?: any;
      routingStrategy?: string;
      messageId?: string;
    }>
  ): Promise<EnhancedPayload[]> {
    this.logger.debug(`🔧 Batch enhancing ${payloads.length} payloads`);

    const results = await Promise.allSettled(
      payloads.map(item =>
        this.enhancePayload(
          item.payload,
          item.userId,
          item.clientContext,
          item.routingStrategy,
          item.messageId
        )
      )
    );

    const successful = results
      .filter(r => r.status === 'fulfilled')
      .map(r => (r as PromiseFulfilledResult<EnhancedPayload>).value);

    const failed = results.filter(r => r.status === 'rejected').length;

    this.logger.debug(`📊 Batch enhancement completed: ${successful.length} successful, ${failed} failed`);

    return successful;
  }

  /**
   * 验证增强后的payload
   */
  validateEnhancedPayload(payload: EnhancedPayload): boolean {
    try {
      // 检查必需字段
      if (!payload.userId || !payload.wsContext) {
        return false;
      }

      // 检查WebSocket上下文
      if (!payload.wsContext.timestamp || !payload.wsContext.routingStrategy) {
        return false;
      }

      // 如果有区服上下文，验证其完整性
      if (payload.serverContext) {
        if (!payload.serverContext.serverId || !payload.serverContext.characterId) {
          return false;
        }
      }

      return true;

    } catch (error) {
      this.logger.error(`❌ Payload validation failed:`, error);
      return false;
    }
  }

  /**
   * 获取payload的摘要信息（用于日志）
   */
  getPayloadSummary(payload: EnhancedPayload): string {
    const summary = {
      userId: payload.userId,
      strategy: payload.wsContext?.routingStrategy,
      serverId: payload.serverContext?.serverId,
      characterId: payload.serverContext?.characterId,
      messageType: payload._messageType,
    };

    return JSON.stringify(summary);
  }

  /**
   * 确定全服消息的优先级
   */
  private determineGlobalMessagePriority(payload: any): number {
    // 根据消息内容确定优先级
    // 这里可以实现复杂的优先级算法
    if (payload.emergency) return 5;
    if (payload.urgent) return 4;
    if (payload.important) return 3;
    return 2; // 默认优先级
  }
}
