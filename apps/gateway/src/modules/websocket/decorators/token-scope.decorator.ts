import { SetMetadata } from '@nestjs/common';

export const TOKEN_SCOPE_KEY = 'tokenScope';

/**
 * Token作用域装饰器（已简化）
 * 由于游戏WebSocket只支持角色Token，此装饰器主要用于向后兼容
 */
export const TokenScope = (scope: 'character') => SetMetadata(TOKEN_SCOPE_KEY, scope);

/**
 * 角色Token装饰器
 * 标记WebSocket事件需要角色级Token访问（游戏中的默认要求）
 *
 * @example
 * ```typescript
 * @CharacterToken()
 * @SubscribeMessage('character.getInfo')
 * async getCharacterInfo(client: Socket, data: any) {
 *   // 所有游戏WebSocket事件都需要角色Token
 *   // 可以从client.metadata获取角色上下文
 * }
 * ```
 */
export const CharacterToken = () => TokenScope('character');

/**
 * @deprecated 游戏WebSocket不支持账号Token，请使用CharacterToken
 */
export const AccountToken = () => {
  throw new Error('Account tokens are not supported in game WebSocket connections. Use CharacterToken instead.');
};
