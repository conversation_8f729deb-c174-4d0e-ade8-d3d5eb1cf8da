import { Injectable, CanActivate, ExecutionContext, Logger } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { JwtService } from '@nestjs/jwt';
import { WsException } from '@nestjs/websockets';
import { Socket } from 'socket.io';
import { ConfigService } from '@nestjs/config';

// 装饰器
import { TOKEN_SCOPE_KEY } from '../decorators/token-scope.decorator';

// 新增：Token类型定义
interface TokenPayload {
  sub: string;
  username: string;
  email: string;
  roles: string[];
  permissions: string[];
  sessionId: string;
  deviceId?: string;
  scope: 'account' | 'character';
  iat: number;
  exp: number;
  jti: string;
}

interface CharacterTokenPayload extends TokenPayload {
  characterId: string;
  serverId: string;
  scope: 'character';
}

/**
 * WebSocket 认证守卫（优化版）
 *
 * 基于握手阶段的认证结果进行验证，不重复验证Token
 * 专为游戏角色连接设计，所有连接都必须是角色级Token
 */
@Injectable()
export class WsAuthGuard implements CanActivate {
  private readonly logger = new Logger(WsAuthGuard.name);

  constructor(
    private readonly reflector: Reflector,
  ) {}

  /**
   * 验证 WebSocket 连接的认证状态
   */
  async canActivate(context: ExecutionContext): Promise<boolean> {
    this.logger.debug(`🚪 WebSocket认证守卫开始执行...`);

    try {
      const client: Socket = context.switchToWs().getClient();
      this.logger.debug(`🚪 获取到WebSocket客户端: ${client.id}`);

      // 检查是否为公开事件
      const isPublic = this.reflector.getAllAndOverride<boolean>('isPublic', [
        context.getHandler(),
        context.getClass(),
      ]);

      if (isPublic) {
        this.logger.debug(`🚪 公开事件，跳过认证`);
        return true;
      }

      this.logger.debug(`🚪 私有事件，需要认证`);
      const token = this.extractTokenFromSocket(client);

      if (!token) {
        this.logger.warn(`❌ WebSocket认证失败: 未提供Token`);
        this.logger.warn(`🔍 握手信息调试:`);
        this.logger.warn(`   - auth对象: ${JSON.stringify(client.handshake.auth)}`);
        this.logger.warn(`   - query参数: ${JSON.stringify(client.handshake.query)}`);
        this.logger.warn(`   - authorization头: ${client.handshake.headers?.authorization}`);
        throw new WsException('Authentication required');
      }

      this.logger.debug(`✅ Token提取成功，开始验证...`);

      // 验证双层Token
      const payload = await this.validateDualToken(token);

      // 将用户信息附加到 socket 对象
      client.data.user = {
        id: payload.sub,
        username: payload.username,
        email: payload.email,
        roles: payload.roles || [],
        permissions: payload.permissions || [],
        sessionId: payload.sessionId,
        deviceId: payload.deviceId,
        tokenScope: payload.scope,
      };

      // 如果是角色Token，附加角色上下文
      if (payload.scope === 'character') {
        const characterPayload = payload as CharacterTokenPayload;
        client.data.character = {
          characterId: characterPayload.characterId,
          serverId: characterPayload.serverId,
        };
      }

      // 检查Token作用域
      const requiredScope = this.reflector.get<'account' | 'character'>(
        TOKEN_SCOPE_KEY,
        context.getHandler()
      );

      if (requiredScope && payload.scope !== requiredScope) {
        throw new WsException(`Token作用域不匹配，期望: ${requiredScope}, 实际: ${payload.scope}`);
      }

      // 检查所需权限
      const requiredRoles = this.reflector.getAllAndOverride<string[]>('roles', [
        context.getHandler(),
        context.getClass(),
      ]);

      if (requiredRoles && requiredRoles.length > 0) {
        const userRoles = payload.roles || [];
        const hasRequiredRole = requiredRoles.some(role => userRoles.includes(role));

        if (!hasRequiredRole) {
          throw new WsException('Insufficient permissions');
        }
      }

      this.logger.debug(`WebSocket authentication successful for user: ${payload.sub}`);
      return true;

    } catch (error) {
      this.logger.error(`WebSocket authentication failed: ${error.message}`);

      if (error.name === 'JsonWebTokenError') {
        throw new WsException('Invalid token');
      } else if (error.name === 'TokenExpiredError') {
        throw new WsException('Token expired');
      } else if (error instanceof WsException) {
        throw error;
      } else {
        throw new WsException('Authentication failed');
      }
    }
  }

  /**
   * 从 Socket 连接中提取 JWT 令牌
   */
  private extractTokenFromSocket(client: Socket): string | null {
    this.logger.debug(`🔍 开始提取WebSocket Token...`);

    // 方法1: 从 auth 对象中获取（推荐方式）
    const authToken = client.handshake.auth?.token;
    this.logger.debug(`🔍 Auth对象Token: ${authToken ? '存在' : '不存在'}`);
    if (authToken && typeof authToken === 'string') {
      this.logger.debug(`✅ 从auth对象获取到Token，长度: ${authToken.length}`);
      return authToken;
    }

    // 方法2: 从查询参数中获取
    const queryToken = client.handshake.query?.token;
    this.logger.debug(`🔍 查询参数Token: ${queryToken ? '存在' : '不存在'}`);
    if (queryToken && typeof queryToken === 'string') {
      this.logger.debug(`✅ 从查询参数获取到Token，长度: ${queryToken.length}`);
      return queryToken;
    }

    // 方法3: 从认证头中获取
    const authHeader = client.handshake.headers?.authorization;
    this.logger.debug(`🔍 Authorization头: ${authHeader ? '存在' : '不存在'}`);
    if (authHeader && typeof authHeader === 'string') {
      const [type, token] = authHeader.split(' ');
      if (type === 'Bearer' && token) {
        this.logger.debug(`✅ 从Authorization头获取到Token，长度: ${token.length}`);
        return token;
      }
    }

    // 方法4: 从 Cookie 中获取
    const cookies = client.handshake.headers?.cookie;
    this.logger.debug(`🔍 Cookie: ${cookies ? '存在' : '不存在'}`);
    if (cookies) {
      const tokenMatch = cookies.match(/token=([^;]+)/);
      if (tokenMatch) {
        this.logger.debug(`✅ 从Cookie获取到Token，长度: ${tokenMatch[1].length}`);
        return tokenMatch[1];
      }
    }

    this.logger.warn(`❌ 所有方法都未能获取到Token`);
    return null;
  }

  /**
   * 验证双层Token
   * 支持账号Token和角色Token的验证
   */
  private async validateDualToken(token: string): Promise<TokenPayload> {
    this.logger.debug(`🔐 开始验证Token，长度: ${token.length}`);

    try {
      // 首先尝试解析Token获取基本信息（不验证签名）
      this.logger.debug(`🔐 步骤1: 解析Token基本信息...`);
      const decoded = this.jwtService.decode(token) as any;

      if (!decoded) {
        this.logger.error(`❌ Token解析失败: 无法解码Token`);
        throw new WsException('Invalid token format');
      }

      this.logger.debug(`🔐 Token解析成功，scope: ${decoded.scope}, sub: ${decoded.sub}`);

      if (!decoded.scope) {
        this.logger.error(`❌ Token验证失败: 缺少scope字段`);
        throw new WsException('Invalid token format');
      }

      // 根据scope字段选择验证方法
      this.logger.debug(`🔐 步骤2: 根据scope验证Token...`);
      switch (decoded.scope) {
        case 'account':
          this.logger.debug(`🔐 使用账号Token验证方法`);
          return await this.validateAccountToken(token);
        case 'character':
          this.logger.debug(`🔐 使用角色Token验证方法`);
          return await this.validateCharacterToken(token);
        default:
          this.logger.error(`❌ 不支持的Token scope: ${decoded.scope}`);
          throw new WsException(`Unsupported token scope: ${decoded.scope}`);
      }

    } catch (error) {
      this.logger.error(`❌ Token验证过程出错:`, error.message);
      if (error instanceof WsException) {
        throw error;
      }
      throw new WsException('Token validation failed');
    }
  }

  /**
   * 验证账号Token
   */
  private async validateAccountToken(token: string): Promise<TokenPayload> {
    this.logger.debug(`🔑 开始验证账号Token...`);

    const accountSecret = this.configService.get<string>('gateway.security.jwtSecret');

    // 调试日志：验证JWT密钥配置
    this.logger.debug(`🔑 JWT密钥配置: ${accountSecret ? '已配置' : '未配置'}`);
    this.logger.debug(`🔑 JWT密钥长度: ${accountSecret?.length || 0}`);

    if (!accountSecret) {
      this.logger.error(`❌ JWT密钥未配置`);
      throw new WsException('JWT secret not configured');
    }

    try {
      this.logger.debug(`🔑 使用JWT密钥验证Token...`);
      const payload = this.jwtService.verify(token, { secret: accountSecret }) as TokenPayload;

      this.logger.debug(`✅ Token签名验证成功，用户: ${payload.sub}`);

      // 验证Token类型
      if (payload.scope !== 'account') {
        this.logger.error(`❌ Token scope验证失败: 期望'account'，实际'${payload.scope}'`);
        throw new WsException('Expected account token');
      }

      // 验证权限边界：账号Token不应包含角色信息
      if ((payload as any).characterId || (payload as any).serverId) {
        this.logger.error(`❌ 账号Token包含角色信息，违反权限边界`);
        throw new WsException('Account token should not contain character information');
      }

      this.logger.debug(`✅ 账号Token验证完全成功`);
      return payload;

    } catch (error) {
      this.logger.error(`❌ JWT验证失败:`, error.message);
      throw new WsException(`JWT verification failed: ${error.message}`);
    }
  }

  /**
   * 验证角色Token
   */
  private async validateCharacterToken(token: string): Promise<CharacterTokenPayload> {
    const characterSecret = this.configService.get<string>('gateway.security.characterJwtSecret') ||
                           this.configService.get<string>('gateway.security.jwtSecret');

    const payload = this.jwtService.verify(token, { secret: characterSecret }) as CharacterTokenPayload;

    // 验证Token类型
    if (payload.scope !== 'character') {
      throw new WsException('Expected character token');
    }

    // 验证权限边界：角色Token必须包含角色和区服信息
    if (!payload.characterId || !payload.serverId) {
      throw new WsException('Character token must contain character and server information');
    }

    return payload;
  }
}
