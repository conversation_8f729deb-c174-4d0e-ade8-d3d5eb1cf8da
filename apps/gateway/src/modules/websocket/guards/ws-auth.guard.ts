import { Injectable, CanActivate, ExecutionContext, Logger } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { WsException } from '@nestjs/websockets';

/**
 * WebSocket 认证守卫（优化版）
 *
 * 基于握手阶段的认证结果进行验证，不重复验证Token
 * 专为游戏角色连接设计，所有连接都必须是角色级Token
 */
@Injectable()
export class WsAuthGuard implements CanActivate {
  private readonly logger = new Logger(WsAuthGuard.name);

  constructor(
    private readonly reflector: Reflector,
  ) {}

  /**
   * 验证 WebSocket 连接的认证状态（优化版）
   * 基于握手阶段的认证结果，不重复验证Token
   */
  async canActivate(context: ExecutionContext): Promise<boolean> {
    this.logger.debug(`🚪 WebSocket认证守卫开始执行...`);

    try {
      const client: any = context.switchToWs().getClient();
      this.logger.debug(`🚪 获取到WebSocket客户端: ${client.id}`);

      // 检查是否为公开事件
      const isPublic = this.reflector.getAllAndOverride<boolean>('isPublic', [
        context.getHandler(),
        context.getClass(),
      ]);

      if (isPublic) {
        this.logger.debug(`🚪 公开事件，跳过认证`);
        return true;
      }

      this.logger.debug(`🚪 私有事件，检查握手阶段认证结果`);

      // 检查握手阶段的认证结果
      if (!client.authenticated || !client.userId) {
        this.logger.warn(`❌ WebSocket认证失败: 握手阶段未通过认证`);
        throw new WsException('Not authenticated');
      }

      // 由于所有WebSocket都是游戏角色API，确保有角色信息
      if (!client.metadata?.characterId || !client.metadata?.serverId) {
        this.logger.warn(`❌ WebSocket认证失败: 缺少角色上下文信息`);
        throw new WsException('Character context required');
      }

      // 检查所需权限（保留权限检查功能）
      const requiredRoles = this.reflector.getAllAndOverride<string[]>('roles', [
        context.getHandler(),
        context.getClass(),
      ]);

      if (requiredRoles && requiredRoles.length > 0) {
        const userRoles = client.user?.roles || [];
        const hasRequiredRole = requiredRoles.some(role => userRoles.includes(role));

        if (!hasRequiredRole) {
          this.logger.warn(`❌ WebSocket权限不足: 需要角色 ${requiredRoles.join(',')}`);
          throw new WsException('Insufficient permissions');
        }
      }

      this.logger.debug(`✅ WebSocket认证成功，用户: ${client.userId}, 角色: ${client.metadata.characterId}`);
      return true;

    } catch (error) {
      this.logger.error(`WebSocket authentication failed: ${error.message}`);

      if (error instanceof WsException) {
        throw error;
      } else {
        throw new WsException('Authentication failed');
      }
    }
  }

}
