import { Injectable, Logger } from '@nestjs/common';
import { RedisService } from '@common/redis';
import { DataType, DATA_TYPES } from '@common/redis/types/redis.types';

/**
 * 会话服务 - 专注多实例协调
 *
 * 核心职责：
 * 1. 跨实例用户定位
 * 2. 全局消息路由
 * 3. 多设备连接管理
 * 4. 业务状态同步（最小化）
 */

export interface UserConnection {
  userId: string;
  instanceId: string;        // 网关实例ID
  socketId: string;          // 当前socketId
  connectedAt: Date;         // 连接时间
  lastSeen: Date;           // 最后活跃时间
  deviceInfo?: {            // 设备标识（用于多设备管理）
    deviceId: string;
    deviceType: string;
  };
}

@Injectable()
export class SessionService {
  private readonly logger = new Logger(SessionService.name);
  private readonly instanceId: string;

  // Redis键设计
  private readonly USER_CONNECTIONS_PREFIX = 'ws:user_connections:';  // 用户的所有连接
  private readonly INSTANCE_CONNECTIONS_PREFIX = 'ws:instance:';      // 实例的所有连接
  private readonly GLOBAL_USERS_SET = 'ws:online_users';              // 全局在线用户集合

  constructor(private readonly redisService: RedisService) {
    this.instanceId = process.env.INSTANCE_ID || `gateway_${Date.now()}`;
  }

  /**
   * 用户连接时注册
   */
  async registerConnection(userId: string, socketId: string, deviceInfo?: any): Promise<void> {
    const connection: UserConnection = {
      userId,
      instanceId: this.instanceId,
      socketId,
      connectedAt: new Date(),
      lastSeen: new Date(),
      deviceInfo,
    };

    const connectionKey = `${userId}:${this.instanceId}:${socketId}`;

    try {
      // 1. 添加到用户连接列表
      await this.redisService.hset(
        `${this.USER_CONNECTIONS_PREFIX}${userId}`,
        connectionKey,
        JSON.stringify(connection),
        DATA_TYPES.GLOBAL
      );

      // 2. 添加到实例连接列表
      await this.redisService.sadd(
        `${this.INSTANCE_CONNECTIONS_PREFIX}${this.instanceId}`,
        `${userId}:${socketId}`,
        DATA_TYPES.GLOBAL
      );

      // 3. 添加到全局在线用户集合
      await this.redisService.sadd(
        this.GLOBAL_USERS_SET,
        userId,
        DATA_TYPES.GLOBAL
      );

      // 4. 设置过期时间（防止僵尸数据）
      await this.redisService.expire(`${this.USER_CONNECTIONS_PREFIX}${userId}`, 3600, DATA_TYPES.GLOBAL);
      await this.redisService.expire(`${this.INSTANCE_CONNECTIONS_PREFIX}${this.instanceId}`, 3600, DATA_TYPES.GLOBAL);

      this.logger.debug(`Connection registered: ${userId} -> ${this.instanceId}:${socketId}`);
    } catch (error) {
      this.logger.error(`Failed to register connection: ${error.message}`);
    }
  }

  /**
   * 用户断开连接时清理
   */
  async unregisterConnection(userId: string, socketId: string): Promise<void> {
    const connectionKey = `${userId}:${this.instanceId}:${socketId}`;

    try {
      // 1. 从用户连接列表移除
      await this.redisService.hdel(
        `${this.USER_CONNECTIONS_PREFIX}${userId}`,
        [connectionKey],
        DATA_TYPES.GLOBAL
      );

      // 2. 从实例连接列表移除
      await this.redisService.srem(
        `${this.INSTANCE_CONNECTIONS_PREFIX}${this.instanceId}`,
        `${userId}:${socketId}`,
        DATA_TYPES.GLOBAL
      );

      // 3. 检查用户是否还有其他连接
      const connectionsData = await this.redisService.hgetall(
        `${this.USER_CONNECTIONS_PREFIX}${userId}`,
        DATA_TYPES.GLOBAL
      );
      const remainingConnections = Object.keys(connectionsData).length;

      if (remainingConnections === 0) {
        // 用户完全离线，从全局在线用户集合移除
        await this.redisService.srem(this.GLOBAL_USERS_SET, userId, DATA_TYPES.GLOBAL);
        await this.redisService.del(`${this.USER_CONNECTIONS_PREFIX}${userId}`, DATA_TYPES.GLOBAL);
      }

      this.logger.debug(`Connection unregistered: ${userId} -> ${this.instanceId}:${socketId}`);
    } catch (error) {
      this.logger.error(`Failed to unregister connection: ${error.message}`);
    }
  }

  /**
   * 获取用户的所有连接（跨实例）
   */
  async getUserConnections(userId: string): Promise<UserConnection[]> {
    try {
      const connectionsData = await this.redisService.hgetall(
        `${this.USER_CONNECTIONS_PREFIX}${userId}`,
        DATA_TYPES.GLOBAL
      );

      return Object.values(connectionsData).map(data => JSON.parse(data as string));
    } catch (error) {
      this.logger.error(`Failed to get user connections: ${error.message}`);
      return [];
    }
  }

  /**
   * 检查用户是否在线（任意实例）
   */
  async isUserOnline(userId: string): Promise<boolean> {
    try {
      return await this.redisService.sismember(this.GLOBAL_USERS_SET, userId, DATA_TYPES.GLOBAL);
    } catch (error) {
      this.logger.error(`Failed to check user online status: ${error.message}`);
      return false;
    }
  }

  /**
   * 获取在线用户数量
   */
  async getOnlineUserCount(): Promise<number> {
    try {
      const members = await this.redisService.smembers(this.GLOBAL_USERS_SET, DATA_TYPES.GLOBAL);
      return members.length;
    } catch (error) {
      this.logger.error(`Failed to get online user count: ${error.message}`);
      return 0;
    }
  }

  /**
   * 清理当前实例的所有连接（实例关闭时调用）
   */
  async cleanupInstanceConnections(): Promise<void> {
    try {
      const connections = await this.redisService.smembers(
        `${this.INSTANCE_CONNECTIONS_PREFIX}${this.instanceId}`,
        DATA_TYPES.GLOBAL
      );

      for (const connection of connections) {
        const [userId, socketId] = connection.split(':');
        await this.unregisterConnection(userId, socketId);
      }

      // 删除实例连接集合
      await this.redisService.del(
        `${this.INSTANCE_CONNECTIONS_PREFIX}${this.instanceId}`,
        DATA_TYPES.GLOBAL
      );

      this.logger.log(`Cleaned up ${connections.length} connections for instance ${this.instanceId}`);
    } catch (error) {
      this.logger.error(`Failed to cleanup instance connections: ${error.message}`);
    }
  }

  /**
   * 更新连接活跃时间
   */
  async updateLastSeen(userId: string, socketId: string): Promise<void> {
    const connectionKey = `${userId}:${this.instanceId}:${socketId}`;

    try {
      const connectionData = await this.redisService.hget(
        `${this.USER_CONNECTIONS_PREFIX}${userId}`,
        connectionKey,
        DATA_TYPES.GLOBAL
      );

      if (connectionData) {
        const connection: UserConnection = JSON.parse(connectionData as string);
        connection.lastSeen = new Date();

        await this.redisService.hset(
          `${this.USER_CONNECTIONS_PREFIX}${userId}`,
          connectionKey,
          JSON.stringify(connection),
          DATA_TYPES.GLOBAL
        );
      }
    } catch (error) {
      this.logger.error(`Failed to update last seen: ${error.message}`);
    }
  }
}
