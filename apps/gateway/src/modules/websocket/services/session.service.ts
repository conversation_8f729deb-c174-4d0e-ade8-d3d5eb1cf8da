import { Injectable, Logger } from '@nestjs/common';
import { RedisService } from '@common/redis';
import { DataType, DATA_TYPES } from '@common/redis/types/redis.types';

/**
 * WebSocket 会话管理服务
 *
 * 负责管理 WebSocket 连接的会话信息，包括：
 * - 用户会话创建和销毁
 * - 会话状态管理
 * - 活跃度跟踪
 * - 多设备连接支持
 */

export interface Session {
  userId: string;
  socketId: string;
  createdAt: Date;
  lastActivity: Date;
  metadata?: Record<string, any>;
}

@Injectable()
export class SessionService {
  private readonly logger = new Logger(SessionService.name);
  private readonly SESSION_PREFIX = 'ws:session:';
  private readonly USER_SESSIONS_PREFIX = 'ws:user:';
  private readonly SOCKET_SESSION_PREFIX = 'ws:socket:';

  constructor(private readonly redisService: RedisService) {}

  /**
   * 创建用户会话
   */
  async createSession(userId: string, socketId: string, metadata?: Record<string, any>): Promise<void> {
    try {
      const session: Session = {
        userId,
        socketId,
        createdAt: new Date(),
        lastActivity: new Date(),
        metadata: metadata || {},
      };

      const sessionKey = `${this.SESSION_PREFIX}${userId}:${socketId}`;
      const userSessionsKey = `${this.USER_SESSIONS_PREFIX}${userId}`;
      const socketSessionKey = `${this.SOCKET_SESSION_PREFIX}${socketId}`;

      // 🎯 存储会话信息 (使用global类型，因为会话是全局数据)
      await this.redisService.set(sessionKey, session, 24 * 60 * 60, DATA_TYPES.GLOBAL);

      // 🎯 添加到用户会话列表 (使用RedisService封装方法)
      await this.redisService.sadd(userSessionsKey, socketId, DATA_TYPES.GLOBAL);
      await this.redisService.expire(userSessionsKey, 24 * 60 * 60, DATA_TYPES.GLOBAL);

      // 🎯 存储 socket 到用户的映射
      await this.redisService.set(socketSessionKey, userId, 24 * 60 * 60, DATA_TYPES.GLOBAL);

      this.logger.log(`Session created for user ${userId}, socket ${socketId}`);
    } catch (error) {
      this.logger.error(`Failed to create session: ${error.message}`);
      throw error;
    }
  }

  /**
   * 获取用户会话
   */
  async getSession(userId: string, socketId: string): Promise<Session | null> {
    try {
      const sessionKey = `${this.SESSION_PREFIX}${userId}:${socketId}`;
      // 🎯 使用global类型获取会话数据
      const sessionData = await this.redisService.get<Session>(sessionKey, DATA_TYPES.GLOBAL);

      if (!sessionData) {
        return null;
      }

      // RedisService已经自动进行了JSON反序列化，直接返回
      return sessionData;
    } catch (error) {
      this.logger.error(`Failed to get session: ${error.message}`);
      return null;
    }
  }

  /**
   * 根据 Socket ID 获取会话
   */
  async getSessionBySocketId(socketId: string): Promise<Session | null> {
    try {
      const socketSessionKey = `${this.SOCKET_SESSION_PREFIX}${socketId}`;
      // 🎯 使用global类型获取socket到用户的映射
      const userId = await this.redisService.get<string>(socketSessionKey, DATA_TYPES.GLOBAL);

      if (!userId) {
        return null;
      }

      return this.getSession(userId, socketId);
    } catch (error) {
      this.logger.error(`Failed to get session by socket ID: ${error.message}`);
      return null;
    }
  }

  /**
   * 获取用户的所有会话
   */
  async getUserSessions(userId: string): Promise<Session[]> {
    try {
      const userSessionsKey = `${this.USER_SESSIONS_PREFIX}${userId}`;
      // 🎯 使用RedisService封装的smembers方法，指定global类型
      const socketIds = await this.redisService.smembers(userSessionsKey, DATA_TYPES.GLOBAL);

      const sessions: Session[] = [];
      for (const socketId of socketIds) {
        const session = await this.getSession(userId, socketId);
        if (session) {
          sessions.push(session);
        }
      }

      return sessions;
    } catch (error) {
      this.logger.error(`Failed to get user sessions: ${error.message}`);
      return [];
    }
  }

  /**
   * 更新会话活跃度
   */
  async updateLastActivity(userId: string, socketId?: string): Promise<void> {
    try {
      if (socketId) {
        // 🎯 更新特定会话
        const session = await this.getSession(userId, socketId);
        if (session) {
          session.lastActivity = new Date();
          const sessionKey = `${this.SESSION_PREFIX}${userId}:${socketId}`;
          await this.redisService.set(sessionKey, session, 24 * 60 * 60, DATA_TYPES.GLOBAL);
        }
      } else {
        // 🎯 更新用户的所有会话
        const sessions = await this.getUserSessions(userId);
        for (const session of sessions) {
          session.lastActivity = new Date();
          const sessionKey = `${this.SESSION_PREFIX}${userId}:${session.socketId}`;
          await this.redisService.set(sessionKey, session, 24 * 60 * 60, DATA_TYPES.GLOBAL);
        }
      }
    } catch (error) {
      this.logger.error(`Failed to update last activity: ${error.message}`);
    }
  }

  /**
   * 移除会话
   */
  async removeSession(userId: string, socketId: string): Promise<void> {
    try {
      const sessionKey = `${this.SESSION_PREFIX}${userId}:${socketId}`;
      const userSessionsKey = `${this.USER_SESSIONS_PREFIX}${userId}`;
      const socketSessionKey = `${this.SOCKET_SESSION_PREFIX}${socketId}`;

      // 🎯 删除会话信息
      await this.redisService.del(sessionKey, DATA_TYPES.GLOBAL);

      // 🎯 从用户会话列表中移除 (使用RedisService封装方法)
      await this.redisService.srem(userSessionsKey, socketId, DATA_TYPES.GLOBAL);

      // 🎯 删除 socket 映射
      await this.redisService.del(socketSessionKey, DATA_TYPES.GLOBAL);

      this.logger.log(`Session removed for user ${userId}, socket ${socketId}`);
    } catch (error) {
      this.logger.error(`Failed to remove session: ${error.message}`);
    }
  }

  /**
   * 通用会话数据存储（用于测试和临时数据）
   */
  async setSessionData(key: string, value: string, ttl?: number): Promise<void> {
    try {
      // 🎯 使用global类型存储会话数据
      await this.redisService.set(key, value, ttl || 3600, DATA_TYPES.GLOBAL); // 默认1小时过期
      this.logger.debug(`Session data set: ${key}`);
    } catch (error) {
      this.logger.error(`Failed to set session data: ${error.message}`);
      throw error;
    }
  }

  /**
   * 获取通用会话数据
   */
  async getSessionData(key: string): Promise<string | null> {
    try {
      // 🎯 使用global类型获取会话数据
      return await this.redisService.get<string>(key, DATA_TYPES.GLOBAL);
    } catch (error) {
      this.logger.error(`Failed to get session data: ${error.message}`);
      return null;
    }
  }

  /**
   * 删除通用会话数据
   */
  async deleteSessionData(key: string): Promise<void> {
    try {
      // 🎯 使用global类型删除会话数据
      await this.redisService.del(key, DATA_TYPES.GLOBAL);
      this.logger.debug(`Session data deleted: ${key}`);
    } catch (error) {
      this.logger.error(`Failed to delete session data: ${error.message}`);
    }
  }

  /**
   * 移除用户的所有会话
   */
  async removeAllUserSessions(userId: string): Promise<void> {
    try {
      const sessions = await this.getUserSessions(userId);
      
      for (const session of sessions) {
        await this.removeSession(userId, session.socketId);
      }

      this.logger.log(`All sessions removed for user ${userId}`);
    } catch (error) {
      this.logger.error(`Failed to remove all user sessions: ${error.message}`);
    }
  }

  /**
   * 清理过期会话
   */
  async cleanupExpiredSessions(): Promise<void> {
    try {
      // Redis 的 TTL 会自动清理过期的键
      // 这里可以添加额外的清理逻辑
      this.logger.debug('Session cleanup completed');
    } catch (error) {
      this.logger.error(`Failed to cleanup expired sessions: ${error.message}`);
    }
  }

  /**
   * 获取会话统计信息
   */
  async getSessionStats(): Promise<{
    totalSessions: number;
    activeUsers: number;
  }> {
    try {
      // 这里可以实现更复杂的统计逻辑
      // 目前返回基础统计
      return {
        totalSessions: 0,
        activeUsers: 0,
      };
    } catch (error) {
      this.logger.error(`Failed to get session stats: ${error.message}`);
      return {
        totalSessions: 0,
        activeUsers: 0,
      };
    }
  }
}
