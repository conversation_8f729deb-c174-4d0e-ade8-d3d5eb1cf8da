# WebSocket代理系统深度分析报告
报告深入分析了网关服务的WebSocket代理机制，包括权限认证、双层payload机制、payload注入逻辑以及Token管理策略。

## 🚨 问题分析

#### 1. 逻辑冗余分析

**存在问题：**

**重复的Token验证：**
1. **握手阶段**：websocket.gateway.ts中的`authenticateSocket()` 验证Token
2. **消息阶段**：ws-auth.guard.ts中的`WsAuthGuard.canActivate()` 再次验证

**解决方案：**

1. 保留握手阶段的Token验证，移除消息阶段的Token验证。
2. 消息阶段不再获取Token。

### 2. Token验证机制分析

#### 2.1 握手阶段Token验证

**验证流程：**
```typescript
// 1. Token提取（4种方式）
const token = socket.handshake.auth?.token || 
              socket.handshake.query?.token ||
              extractFromAuthHeader(socket) ||
              extractFromCookie(socket);

// 2. Token解析
const decoded = this.jwtService.decode(token);

// 3. 分类验证
switch (decoded.scope) {
  case 'account':
    payload = this.jwtService.verify(token, { secret: accountSecret });
    break;
  case 'character':
    payload = this.jwtService.verify(token, { secret: characterSecret });
    break;
}

// 4. 上下文存储
socket.userId = payload.sub;
socket.user = { ... };
socket.metadata = { tokenScope: payload.scope };
if (payload.scope === 'character') {
  socket.metadata.characterId = payload.characterId;
  socket.metadata.serverId = payload.serverId;
}
```

**存在问题：**
- Token提取（4种方式）过度设计
- Token信息分散存储在多个位置
- 没有Token过期的主动处理机制
- 角色Token和账号Token的切换逻辑不清晰

**解决方案：**
1. 我们只需要保留一个正确的当前项目使用的token获取方式即可
2. token信息存储到socket的单一位置
3. websocket建立后除非心跳检测超时端口，否则token持续有效。
4. websocket连接必须持有角色Token。
5. 账号Token相关的操作统一交给http处理。