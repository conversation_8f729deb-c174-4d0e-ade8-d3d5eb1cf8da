/**
 * 微服务WebSocket通信公共库
 * 
 * 提供统一的WebSocket连接、认证、消息发送等功能
 * 可被所有微服务测试脚本复用
 */

const io = require('socket.io-client');
const axios = require('axios');
const chalk = require('chalk');

/**
 * 微服务WebSocket客户端类
 */
class MicroserviceWebSocketClient {
  constructor(config) {
    this.config = {
      GATEWAY_WS_URL: 'http://127.0.0.1:3000',
      AUTH_URL: 'http://127.0.0.1:3001',
      TIMEOUT: 30000,
      ...config
    };
    
    this.socket = null;
    this.token = null;
    this.characterToken = null;
    this.currentTestIndex = 0;
    this.isConnected = false;
  }

  /**
   * HTTP健康检查
   */
  async checkHttpHealth(serviceName, url) {
    try {
      const start = Date.now();

      // 根据服务名称确定健康检查路径
      let healthPath = '/health';
      if (serviceName === 'Match服务') {
        healthPath = '/api/health';
      }

      const response = await axios.get(url + healthPath, {
        timeout: 5000,
        family: 4
      });
      const duration = Date.now() - start;

      if (response.status === 200) {
        // 对于网关服务，即使返回unhealthy状态也认为服务可用（只要能响应）
        if (serviceName === '网关服务') {
          console.log(chalk.green(`✅ ${serviceName} HTTP健康检查: 服务可用 (${duration}ms)`));
        } else {
          console.log(chalk.green(`✅ ${serviceName} HTTP健康检查: 正常 (${duration}ms)`));
        }
        return true;
      } else {
        console.log(chalk.red(`❌ ${serviceName} HTTP健康检查: 异常 (状态码: ${response.status})`));
        return false;
      }
    } catch (error) {
      console.log(chalk.red(`❌ ${serviceName} HTTP健康检查失败: ${error.message}`));
      return false;
    }
  }

  /**
   * 获取认证令牌
   */
  async getAuthToken(userPrefix = 'test') {
    try {
      console.log(chalk.yellow('🔑 获取认证令牌...'));
      
      // 先注册用户（通过网关）
      const username = `${userPrefix}_${Date.now()}`;
      const email = `${userPrefix}_${Date.now()}@example.com`;
      const password = 'SecureP@ssw0rd!';

      const registerResponse = await axios.post(`${this.config.GATEWAY_WS_URL}/api/auth/auth/register`, {
        username: username,
        email: email,
        password: password,
        confirmPassword: password,
        acceptTerms: true,
        profile: {
          firstName: 'Test',
          lastName: 'User'
        }
      }, { timeout: 10000 });

      if (registerResponse.data.success) {
        console.log(chalk.green('✅ 用户注册成功'));

        // 然后登录获取token（通过网关）
        const loginResponse = await axios.post(`${this.config.GATEWAY_WS_URL}/api/auth/auth/login`, {
          identifier: username,  // 使用identifier而不是username
          password: password
        }, { timeout: 10000 });

        // 正确解析token位置（参考character测试脚本的成功模式）
        const accessToken = loginResponse.data.data?.tokens?.accessToken;
        if (loginResponse.status === 200 && accessToken) {
          this.token = accessToken;
          console.log(chalk.green('✅ 认证令牌获取成功'));
          console.log(chalk.gray(`Token长度: ${accessToken.length}`));
          return this.token;
        } else {
          console.log(chalk.red('❌ 登录失败，无法获取令牌'));
          console.log(chalk.red('登录响应:', JSON.stringify(loginResponse.data, null, 2)));
          throw new Error('登录失败或未获取到token');
        }
      } else {
        throw new Error('用户注册失败');
      }
    } catch (error) {
      console.log(chalk.red(`❌ 认证失败: ${error.message}`));

      // 显示详细的错误信息
      if (error.response?.data) {
        console.log(chalk.red('详细错误信息:'));
        if (error.response.data.message) {
          if (Array.isArray(error.response.data.message)) {
            error.response.data.message.forEach((msg, index) => {
              console.log(chalk.red(`  ${index + 1}. ${msg}`));
            });
          } else {
            console.log(chalk.red(`  ${error.response.data.message}`));
          }
        }
        console.log(chalk.gray('完整响应:', JSON.stringify(error.response.data, null, 2)));
      }

      throw error;
    }
  }

  /**
   * 建立WebSocket连接
   */
  async connectWebSocket() {
    return new Promise((resolve, reject) => {
      console.log(chalk.yellow('🔌 建立 WebSocket 连接...'));

      // 使用正确的认证方式：在连接时传递token
      this.socket = io(this.config.GATEWAY_WS_URL, {
        auth: { token: this.characterToken },  // 在连接时传递角色token
        timeout: this.config.TIMEOUT,
        transports: ['websocket', 'polling'],  // 支持多种传输方式
        forceNew: true
      });

      this.socket.on('connect', () => {
        console.log(chalk.green('✅ WebSocket 连接和认证成功'));
        this.isConnected = true;
        resolve(true);
      });

      this.socket.on('connect_error', (error) => {
        console.log(chalk.red(`❌ WebSocket 连接失败: ${error.message}`));
        this.isConnected = false;
        reject(error);
      });

      this.socket.on('disconnect', (reason, details) => {
        console.log(chalk.yellow(`⚠️ WebSocket 连接断开: ${reason}`));
        if (details) {
          console.log(chalk.gray('断开详情:', details));
        }
        this.isConnected = false;
      });

      this.socket.on('error', (error) => {
        console.log(chalk.red(`❌ Socket错误: ${error.message || error}`));
      });

      setTimeout(() => {
        if (!this.isConnected) {
          reject(new Error('WebSocket连接超时'));
        }
      }, this.config.TIMEOUT);
    });
  }

  /**
   * 发送WebSocket消息并等待响应（采用character测试脚本的成功模式）
   */
  async sendMessage(command, payload = {}) {
    return new Promise((resolve, reject) => {
      if (!this.isConnected || !this.socket) {
        reject(new Error('WebSocket未连接'));
        return;
      }

      const messageId = `test_${Date.now()}_${this.currentTestIndex++}`;
      
      const message = {
        id: messageId,
        command: command,
        payload: {
          ...payload,
          token: this.token
        }
      };

      console.log(chalk.gray(`📤 发送消息: ${command}`));
      console.log(chalk.gray(JSON.stringify(message, null, 2)));

      // 设置响应监听器
      const responseHandler = (response) => {
        if (response.id === messageId) {
          console.log(chalk.cyan(`📨 收到响应: ${command}`));
          console.log(chalk.cyan(JSON.stringify(response, null, 2)));
          this.socket.off('message', responseHandler);
          resolve(response);
        }
      };

      this.socket.on('message', responseHandler);

      // 发送消息
      this.socket.emit('message', message);

      // 设置超时
      setTimeout(() => {
        this.socket.off('message', responseHandler);
        reject(new Error(`消息超时: ${command}`));
      }, this.config.TIMEOUT);
    });
  }

  /**
   * 断开连接
   */
  disconnect() {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
      this.isConnected = false;
      console.log(chalk.gray('🔌 WebSocket连接已断开'));
    }
  }

  /**
   * 获取连接状态
   */
  getConnectionStatus() {
    return {
      isConnected: this.isConnected,
      hasToken: !!this.token,
      socketId: this.socket?.id || null
    };
  }
}

module.exports = MicroserviceWebSocketClient;
